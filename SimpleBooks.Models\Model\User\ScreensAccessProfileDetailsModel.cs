﻿namespace SimpleBooks.Models.Model.User
{
    [Table("ScreensAccessProfileDetails")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<ScreensAccessProfileDetailsModel>))]
    public class ScreensAccessProfileDetailsModel : BaseScreensAccessProfileDetailsModel
    {
        // Override the navigation property to use the derived type
        [Browsable(false)]
        public new virtual ScreensAccessProfileModel ScreensAccessProfile { get; set; } = null!;
    }
}
