﻿namespace SimpleBooks.API.Controllers.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckTreasuryVoucherController : BaseBusinessController<CheckTreasuryVoucherModel, CheckTreasuryVoucherModel, CreateCheckTreasuryVoucherViewModel, UpdateCheckTreasuryVoucherViewModel>
    {
        private readonly ICheckTreasuryVoucherService _checkTreasuryVoucherService;

        public CheckTreasuryVoucherController(ICheckTreasuryVoucherService checkTreasuryVoucherService) : base(checkTreasuryVoucherService)
        {
            _checkTreasuryVoucherService = checkTreasuryVoucherService;
        }

        [HttpGet(nameof(SelectiveCheckTreasuryVoucherDtoListAsync))]
        public async Task<IActionResult> SelectiveCheckTreasuryVoucherDtoListAsync()
        {
            try
            {
                var result = await _checkTreasuryVoucherService.SelectiveCheckTreasuryVoucherDtoListAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet(nameof(SelectiveCheckTreasuryVouchersAsync))]
        public async Task<IActionResult> SelectiveCheckTreasuryVouchersAsync(Ulid checkStatusId)
        {
            try
            {
                var result = await _checkTreasuryVoucherService.SelectiveCheckTreasuryVouchersAsync(checkStatusId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet(nameof(SelectiveDepositCheckTreasuryVouchersAsync))]
        public async Task<IActionResult> SelectiveDepositCheckTreasuryVouchersAsync()
        {
            try
            {
                var result = await _checkTreasuryVoucherService.SelectiveDepositCheckTreasuryVouchersAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet(nameof(GetByIdJsonAsync))]
        public async Task<IActionResult> GetByIdJsonAsync(string id)
        {
            try
            {
                Ulid ulid = Ulid.Parse(id);
                var result = await _checkTreasuryVoucherService.GetByIdJsonAsync(ulid);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
