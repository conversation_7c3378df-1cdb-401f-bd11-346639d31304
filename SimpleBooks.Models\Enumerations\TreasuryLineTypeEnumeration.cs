﻿namespace SimpleBooks.Models.Enumerations
{
    public class TreasuryLineTypeEnumeration : UlidEnumeration<TreasuryLineTypeEnumeration>
    {
        public static readonly TreasuryLineTypeEnumeration Expenses = new TreasuryLineTypeEnumeration(Ulid.Parse("01JTKFY7VKEN2E9533K0R9D233"), "Expenses");
        public static readonly TreasuryLineTypeEnumeration Bill = new TreasuryLineTypeEnumeration(Ulid.Parse("01JTKFYF9KA5J9VP3FF37C1YBD"), "Bill");
        public static readonly TreasuryLineTypeEnumeration BillReturn = new TreasuryLineTypeEnumeration(Ulid.Parse("01JTKGCDENPTX3SJHM4F4RM5E8"), "BillReturn");
        public static readonly TreasuryLineTypeEnumeration Invoice = new TreasuryLineTypeEnumeration(Ulid.Parse("01JTKGCJA6QHV54HQPPN251GGW"), "Invoice");
        public static readonly TreasuryLineTypeEnumeration InvoiceReturn = new TreasuryLineTypeEnumeration(Ulid.Parse("01JTKGCN8AADCYH079T7GGG6PZ"), "InvoiceReturn");

        private TreasuryLineTypeEnumeration(Ulid key, string value) : base(key, value)
        {
        }

        public static List<TreasuryLineTypeEnumeration> TreasuryLineTypeEnumerations
        {
            get => new List<TreasuryLineTypeEnumeration>
            {
                Expenses, Bill, BillReturn, Invoice, InvoiceReturn,
            };
        }
    }
}
