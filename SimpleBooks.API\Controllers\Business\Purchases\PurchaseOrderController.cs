﻿namespace SimpleBooks.API.Controllers.Business.Purchases
{
    public class PurchaseOrderController : BaseBusinessController<PurchaseOrderModel, IndexPurchaseOrderViewModel, CreatePurchaseOrderViewModel, UpdatePurchaseOrderViewModel>
    {
        private readonly IPurchaseOrderService _purchaseOrderService;

        public PurchaseOrderController(IPurchaseOrderService purchaseOrderService) : base(purchaseOrderService)
        {
            _purchaseOrderService = purchaseOrderService;
        }

        [HttpGet(nameof(GetBilledQuantitiesAsync))]
        public async Task<IActionResult> GetBilledQuantitiesAsync(string purchaseOrderId)
        {
            try
            {
                Ulid ulid = Ulid.Parse(purchaseOrderId);
                var result = await _purchaseOrderService.GetBilledQuantitiesAsync(ulid);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet(nameof(GetBillDetailsByOrderAndProductAsync))]
        public async Task<IActionResult> GetBillDetailsByOrderAndProductAsync(string purchaseOrderId, string productId)
        {
            try
            {
                Ulid ulid = Ulid.Parse(purchaseOrderId);
                Ulid ulid2 = Ulid.Parse(productId);
                var result = await _purchaseOrderService.GetBillDetailsByOrderAndProductAsync(ulid, ulid2);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet(nameof(GetReportPurchaseOrderViewModelAsync))]
        public async Task<IActionResult> GetReportPurchaseOrderViewModelAsync(string id)
        {
            try
            {
                Ulid ulid = Ulid.Parse(id);
                var result = await _purchaseOrderService.GetReportPurchaseOrderViewModelAsync(ulid);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }
    }
}
