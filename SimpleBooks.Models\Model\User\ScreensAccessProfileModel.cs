﻿namespace SimpleBooks.Models.Model.User
{
    [Table("ScreensAccessProfile")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<ScreensAccessProfileModel>))]
    public class ScreensAccessProfileModel : BaseScreensAccessProfileModel
    {
        [Browsable(false)]
        [DisplayName("Screens")]
        public new virtual ICollection<ScreensAccessProfileDetailsModel> ScreensAccessProfileDetails { get; set; } = new List<ScreensAccessProfileDetailsModel>();

        [Browsable(false)]
        [DisplayName("Users")]
        public new virtual ICollection<UserModel> Users { get; set; } = new List<UserModel>();
    }
}
