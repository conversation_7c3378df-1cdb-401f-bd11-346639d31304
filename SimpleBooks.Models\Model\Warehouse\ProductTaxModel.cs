﻿namespace SimpleBooks.Models.Model.Warehouse
{
    [Table("ProductTax")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<ProductTaxModel>))]
    public class ProductTaxModel : BaseWithoutTrackingModel
    {
        [CustomRequired]
        [DisplayName("Product Taxs Ratio")]
        [Range(0, 1)]
        public decimal ProductTaxsRatio { get; set; }

        [CustomRequired]
        [DisplayName("Product")]
        public Ulid ProductId { get; set; }
        public virtual ProductModel? Product { get; set; }

        [CustomRequired]
        [DisplayName("Tax Type")]
        public Ulid TaxTypeId { get; set; }
        public virtual TaxTypeModel? TaxType { get; set; }

        [CustomRequired]
        [DisplayName("Tax SubType")]
        public Ulid TaxSubTypeId { get; set; }
        public virtual TaxSubTypeModel? TaxSubType { get; set; }
    }
}
