﻿namespace SimpleBooks.API.Controllers.Business.Warehouse
{
    public class InventoryController : BaseBusinessController<InventoryModel, InventoryModel, CreateInventoryViewModel, UpdateInventoryViewModel>
    {
        private readonly IInventoryService _inventoryService;

        public InventoryController(IInventoryService inventoryService) : base(inventoryService)
        {
            _inventoryService = inventoryService;
        }
    }
}
