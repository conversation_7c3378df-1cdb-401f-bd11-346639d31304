﻿namespace SimpleBooks.Models.Comparers
{
    public class NaturalStringComparer : IComparer<string>
    {
        private static readonly Regex regex = new(@"^(?<prefix>[A-Za-z]+)(?<number>\d+)$", RegexOptions.Compiled);

        public int Compare(string? x, string? y)
        {
            if (x == null || y == null) return string.Compare(x, y);

            var matchX = regex.Match(x);
            var matchY = regex.Match(y);

            if (!matchX.Success || !matchY.Success)
                return string.Compare(x, y, StringComparison.OrdinalIgnoreCase);

            var prefixX = matchX.Groups["prefix"].Value;
            var prefixY = matchY.Groups["prefix"].Value;

            int prefixCompare = string.Compare(prefixX, prefixY, StringComparison.OrdinalIgnoreCase);
            if (prefixCompare != 0)
                return prefixCompare;

            int numberX = int.Parse(matchX.Groups["number"].Value);
            int numberY = int.Parse(matchY.Groups["number"].Value);

            return numberX.CompareTo(numberY);
        }
    }
}
