﻿namespace SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement
{
    [Table("CheckCollection")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<CheckCollectionModel>))]
    public partial class CheckCollectionModel : BaseModel
    {
        [CustomRequired]
        [DisplayName("Collection Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateTime CollectionDate { get; set; }

        [CustomRequired]
        [DisplayName("Check Treasury Vouchers")]
        public virtual ICollection<CheckTreasuryVoucherModel> CheckTreasuryVouchers { get; set; } = new List<CheckTreasuryVoucherModel>();
        [DisplayName("Check Status Histories")]
        public virtual ICollection<CheckStatusHistoryModel> CheckStatusHistories { get; set; } = new List<CheckStatusHistoryModel>();
    }
}
