﻿namespace SimpleBooks.API.Controllers.Business.Treasury.CashManagement
{
    public class CashTreasuryVoucherController : BaseBusinessController<CashTreasuryVoucherModel, CashTreasuryVoucherModel, CreateCashTreasuryVoucherViewModel, UpdateCashTreasuryVoucherViewModel>
    {
        private readonly ICashTreasuryVoucherService _cashTreasuryVoucherService;

        public CashTreasuryVoucherController(ICashTreasuryVoucherService cashTreasuryVoucherService) : base(cashTreasuryVoucherService)
        {
            _cashTreasuryVoucherService = cashTreasuryVoucherService;
        }

        [HttpGet(nameof(SelectiveCashTreasuryVoucherDtoListAsync))]
        public async Task<IActionResult> SelectiveCashTreasuryVoucherDtoListAsync()
        {
            try
            {
                var result = await _cashTreasuryVoucherService.SelectiveCashTreasuryVoucherDtoListAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet(nameof(GetByIdJsonAsync))]
        public async Task<IActionResult> GetByIdJsonAsync(string id)
        {
            try
            {
                Ulid ulid = Ulid.Parse(id);
                var result = await _cashTreasuryVoucherService.GetByIdJsonAsync(ulid);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
