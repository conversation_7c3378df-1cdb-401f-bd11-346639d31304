﻿namespace SimpleBooks.Models.ModelMapper.HR
{
    public static class EmployeeMapper
    {
        public static CreateEmployeeViewModel ToCreateDto(this EmployeeModel entity)
        {
            CreateEmployeeViewModel viewModel = new CreateEmployeeViewModel()
            {
                EmployeeName = entity.EmployeeName,
                EmployeeEMail = entity.EmployeeEMail,
                EmployeePhone = entity.EmployeePhone,
                EmployeeIsRep = entity.EmployeeIsRep,
                UserId = entity.UserId,
            };
            return viewModel;
        }

        public static EmployeeModel ToEntity(this CreateEmployeeViewModel entity)
        {
            EmployeeModel model = new EmployeeModel()
            {
                EmployeeName = entity.EmployeeName,
                EmployeeEMail = entity.EmployeeEMail,
                EmployeePhone = entity.EmployeePhone,
                EmployeeIsRep = entity.EmployeeIsRep,
                UserId = entity.UserId,
            };
            return model;
        }

        public static UpdateEmployeeViewModel ToUpdateDto(this EmployeeModel entity)
        {
            UpdateEmployeeViewModel viewModel = new UpdateEmployeeViewModel()
            {
                Id = entity.Id,
                EmployeeName = entity.EmployeeName,
                EmployeeEMail = entity.EmployeeEMail,
                EmployeePhone = entity.EmployeePhone,
                EmployeeIsRep = entity.EmployeeIsRep,
                UserId = entity.UserId,
            };
            return viewModel;
        }

        public static EmployeeModel ToEntity(this UpdateEmployeeViewModel entity)
        {
            EmployeeModel model = new EmployeeModel()
            {
                Id = entity.Id,
                EmployeeName = entity.EmployeeName,
                EmployeeEMail = entity.EmployeeEMail,
                EmployeePhone = entity.EmployeePhone,
                EmployeeIsRep = entity.EmployeeIsRep,
                UserId = entity.UserId,
            };
            return model;
        }
    }
}
