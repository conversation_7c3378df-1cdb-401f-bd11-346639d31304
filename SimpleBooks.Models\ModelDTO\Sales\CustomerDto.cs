﻿namespace SimpleBooks.Models.ModelDTO.Sales
{
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<CustomerDto>))]
    public class CustomerDto
    {
        public Ulid Id { get; set; }
        public string CustomerName { get; set; }
        public string CustomerTaxCardNumber { get; set; }
        public Ulid? CustomerTypeId { get; set; }
        public Ulid? CustomerRepId { get; set; }
        public Ulid? PaymentTermId { get; set; }
    }
}
