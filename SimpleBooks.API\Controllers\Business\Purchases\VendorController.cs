﻿namespace SimpleBooks.API.Controllers.Business.Purchases
{
    public class VendorController : BaseBusinessController<VendorModel, IndexVendorViewModel, CreateVendorViewModel, UpdateVendorViewModel>
    {
        private readonly IVendorService _vendorService;

        public VendorController(IVendorService vendorService) : base(vendorService)
        {
            _vendorService = vendorService;
        }

        [HttpGet(nameof(SelectiveVendorDtoListAsync))]
        public async Task<IActionResult> SelectiveVendorDtoListAsync()
        {
            try
            {
                var result = await _vendorService.SelectiveVendorDtoListAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
