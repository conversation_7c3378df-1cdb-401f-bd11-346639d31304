﻿namespace SimpleBooks.Models.ModelMapper.Purchases
{
    public static class VendorMapper
    {
        public static CreateVendorViewModel ToCreateDto(this VendorModel entity)
        {
            CreateVendorViewModel viewModel = new CreateVendorViewModel()
            {
                VendorName = entity.VendorName,
                VendorTaxCardNumber = entity.VendorTaxCardNumber,
                VendorTypeId = entity.VendorTypeId,
                PaymentTermId = entity.PaymentTermId,
                ShipViaId = entity.ShipViaId,
                DeliveredById = entity.DeliveredById,
            };
            return viewModel;
        }

        public static VendorModel ToEntity(this CreateVendorViewModel entity)
        {
            VendorModel model = new VendorModel()
            {
                VendorName = entity.VendorName,
                VendorTaxCardNumber = entity.VendorTaxCardNumber,
                VendorTypeId = entity.VendorTypeId,
                PaymentTermId = entity.PaymentTermId,
                ShipViaId = entity.ShipViaId,
                DeliveredById = entity.DeliveredById,
            };
            return model;
        }

        public static UpdateVendorViewModel ToUpdateDto(this VendorModel entity)
        {
            UpdateVendorViewModel viewModel = new UpdateVendorViewModel()
            {
                Id = entity.Id,
                VendorName = entity.VendorName,
                VendorTaxCardNumber = entity.VendorTaxCardNumber,
                VendorTypeId = entity.VendorTypeId,
                PaymentTermId = entity.PaymentTermId,
                ShipViaId = entity.ShipViaId,
                DeliveredById = entity.DeliveredById,
            };
            return viewModel;
        }

        public static VendorModel ToEntity(this UpdateVendorViewModel entity)
        {
            VendorModel model = new VendorModel()
            {
                Id = entity.Id,
                VendorName = entity.VendorName,
                VendorTaxCardNumber = entity.VendorTaxCardNumber,
                VendorTypeId = entity.VendorTypeId,
                PaymentTermId = entity.PaymentTermId,
                ShipViaId = entity.ShipViaId,
                DeliveredById = entity.DeliveredById,
            };
            return model;
        }
    }
}
