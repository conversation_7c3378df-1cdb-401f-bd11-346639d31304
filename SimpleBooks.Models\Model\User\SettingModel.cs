﻿namespace SimpleBooks.Models.Model.User
{
    [Table("Setting")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<SettingModel>))]
    public class SettingModel : BaseWithTrackingModel
    {
        [CustomRequired]
        [Browsable(false)]
        [Searchable(SearchableAttribute.SelectControls.ComboBox, nameof(User))]
        [DisplayName("User")]
        public Ulid UserId { get; set; }
        [Browsable(false)]
        public virtual UserModel User { get; set; } = null!;
    }
}
