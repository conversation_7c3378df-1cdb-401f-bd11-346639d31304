﻿namespace SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement
{
    [Table("CheckVault")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<CheckVaultModel>))]
    public class CheckVaultModel : BaseWithoutTrackingModel
    {
        [CustomRequired]
        [DisplayName("Check Vault Name")]
        public string CheckVaultName { get; set; }

        [CustomRequired]
        [DisplayName("Check Vault Locations")]
        public virtual ICollection<CheckVaultLocationModel> CheckVaultLocations { get; set; } = new List<CheckVaultLocationModel>();
        [DisplayName("Check Treasury Vouchers")]
        public virtual ICollection<CheckTreasuryVoucherModel> CheckTreasuryVouchers { get; set; } = new List<CheckTreasuryVoucherModel>();
        [DisplayName("Check Returns")]
        public virtual ICollection<CheckReturnModel> CheckReturns { get; set; } = new List<CheckReturnModel>();
    }
}
