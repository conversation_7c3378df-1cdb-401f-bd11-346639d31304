﻿namespace GMCadiomCore.Presenters.AutoMapper.BasePresenter
{
    public class BasePresenter<T1, T2> : Presenters.BasePresenter.BasePresenter<T1, T2>, IBasePresenter<T1, T2> where T1 : class, IBaseIdentityModel, INotifyPropertyChanged where T2 : class, IBaseIdentityModel
    {
        //Fields
        public IMapper Mapper { get; private set; }

        //Constructor
        public BasePresenter(IBaseView<T1> view, IBaseUnitOfWork repository, IBaseService<T1, T2> baseService, IMapper mapper) : base(view, repository, baseService)
        {
            Mapper = mapper;
        }
    }
}
