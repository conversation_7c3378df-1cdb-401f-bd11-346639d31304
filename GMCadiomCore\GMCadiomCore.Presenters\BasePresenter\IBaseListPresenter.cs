﻿namespace GMCadiomCore.Presenters.BasePresenter
{
    internal interface IBaseListPresenter<T1, T2> where T1 : class, IBaseIdentityModel where T2 : class, IBaseIdentityModel
    {
        IBaseGenericListView<T2> View { get; }
        IBaseUnitOfWork Repository { get; }
        IBaseService<T1, T2> BaseService { get; }
        BindingSource MainBindingSource { get; }
        PaginationList<T2> MainList { get; }
        T2 CurrentModel { get; }
        string ViewScreenName { get; }
        void LoadAllActionList();
        void AddNewAction();
        void EditAction();
        void DeleteSelectedAction();
        void ImportAction();
        void ExportAction();
        void SearchAction();
    }
}
