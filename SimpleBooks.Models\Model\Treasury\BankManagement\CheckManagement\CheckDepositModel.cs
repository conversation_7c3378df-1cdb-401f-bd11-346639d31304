﻿namespace SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement
{
    [Table("CheckDeposit")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<CheckDepositModel>))]
    public partial class CheckDepositModel : BaseModel
    {
        [CustomRequired]
        [DisplayName("Deposit Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateTime DepositDate { get; set; }
        [DisplayName("Refrance Number")]
        public string? RefranceNumber { get; set; }

        [CustomRequired]
        [DisplayName("Bank")]
        public Ulid BankId { get; set; }
        public virtual BankModel? Bank { get; set; }

        [CustomRequired]
        [DisplayName("Bank Account")]
        public Ulid BankAccountId { get; set; }
        public virtual BankAccountModel? BankAccount { get; set; }

        [CustomRequired]
        [DisplayName("Check Treasury Vouchers")]
        public virtual ICollection<CheckTreasuryVoucherModel> CheckTreasuryVouchers { get; set; } = new List<CheckTreasuryVoucherModel>();
        [DisplayName("Check Status Histories")]
        public virtual ICollection<CheckStatusHistoryModel> CheckStatusHistories { get; set; } = new List<CheckStatusHistoryModel>();
    }
}
