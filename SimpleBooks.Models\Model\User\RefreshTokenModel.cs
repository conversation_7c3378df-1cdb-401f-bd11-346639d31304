﻿namespace SimpleBooks.Models.Model.User
{
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<RefreshTokenModel>))]
    [NotAuditable]
    public class RefreshTokenModel : BaseWithoutTrackingModel
    {
        [CustomRequired]
        [MaxLength(100)]
        [Column(TypeName = "nvarchar(max)")]
        public string Token { get; set; }
        [CustomRequired]
        public DateTime CreatedOn { get; set; }
        [CustomRequired]
        public DateTime ExpiresOn { get; set; }
        public bool IsExpired => DateTime.UtcNow >= ExpiresOn;
        public DateTime? RevokedOn { get; set; }
        public bool IsActive => RevokedOn == null && !IsExpired;

        [CustomRequired]
        [Browsable(false)]
        [Searchable(SearchableAttribute.SelectControls.ComboBox, nameof(UserModel))]
        [DisplayName("User")]
        public Ulid UserId { get; set; }
        [Browsable(false)]
        public virtual UserModel User { get; set; } = null!;
    }
}
