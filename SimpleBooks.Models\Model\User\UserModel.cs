﻿namespace SimpleBooks.Models.Model.User
{
    [Table("AspNetUsers")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<UserModel>))]
    public class UserModel : IdentityUser<Ulid>, IBaseUserModel
    {
        [Key, DatabaseGenerated(DatabaseGeneratedOption.None)]
        public override Ulid Id { get; set; } = Ulid.NewUlid();

        [CustomRequired]
        [DisplayName("User Type")]
        public Ulid UserTypeId { get; set; }
        [CustomRequired]
        [DisplayName("ScreensAccessProfile")]
        public Ulid ScreensAccessProfileId { get; set; }
        [Browsable(false)]
        public virtual ScreensAccessProfileModel ScreensAccessProfile { get; set; }

        [CustomRequired]
        [DisplayName("Employee")]
        public Ulid EmployeeId { get; set; }
        public virtual EmployeeModel Employee { get; set; }

        [CustomRequired]
        [DisplayName("Setting")]
        public Ulid SettingId { get; set; }
        public virtual SettingModel Setting { get; set; }

        [Browsable(false)]
        public virtual ICollection<RefreshTokenModel> RefreshTokens { get; set; } = new List<RefreshTokenModel>();
    }
}
