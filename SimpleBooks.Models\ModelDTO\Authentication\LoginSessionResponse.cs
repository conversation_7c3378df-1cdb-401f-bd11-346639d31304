﻿namespace SimpleBooks.Models.ModelDTO.Authentication
{
    public class LoginSessionResponse
    {
        public string Token { get; set; }
        public DateTime ExpiresOn { get; set; }
        public RefreshTokenResponse RefreshToken { get; set; }
        public EmployeeLoginSessionResponse Employee { get; set; }
    }

    public class EmployeeLoginSessionResponse
    {
        public required Ulid Id { get; set; }
        public required string EmployeeName { get; set; }
        public required UserLoginSessionResponse User { get; set; }
        public required SettingLoginSessionResponse Setting { get; set; }
    }

    public class UserLoginSessionResponse
    {
        public required Ulid Id { get; set; }
        public required string UserName { get; set; }
        public required Ulid UserTypeId { get; set; }
        public required ScreensAccessProfileLoginSessionResponse ScreensAccessProfile { get; set; }
    }

    public class SettingLoginSessionResponse
    {
        public required Ulid Id { get; set; }
    }

    public class ScreensAccessProfileLoginSessionResponse : ScreensAccessProfileModel
    {
    }
}
