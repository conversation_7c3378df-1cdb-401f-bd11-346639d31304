import { inventoryTools } from '/js/business/Warehouse/inventoryTools.js';
import { taxTools } from '/js/business/Tax/taxTools.js';
import { TaxValueModel } from '/js/Business/Tax/TaxValueModel.js';

export class InventoryHandler {
    constructor(products, stores, taxTypes, taxSubTypes, includeHidden, hiddenValue, isSales) {
        this.products = products;
        this.stores = stores;
        this.taxTypes = taxTypes;
        this.taxSubTypes = taxSubTypes;
        this.includeHidden = includeHidden;
        this.hiddenValue = hiddenValue ? new TaxValueModel(hiddenValue) : null;
        this.isSales = isSales;
        this.selectedRowIndex = null;
    }

    onProductChanged(productId, index) {
        const product = this.products?.$values?.find(x => x.Id == productId);
        const productUnits = product?.ProductUnits;
        if (productUnits) {
            inventoryTools.updateProductUnitDropdown(index, productUnits);
        }

        // Set product taxes from the model
        const productTaxes = product?.ProductTaxes;
        if (productTaxes) {
            inventoryTools.updateProductTaxes(index, productTaxes, this.taxTypes);
        }
    }

    onUnitChanged(unitId, index) {
        const row = document.querySelector(`#InventoriesDataGridView tr[data-row-index="${index}"]`);
        if (!row) return;

        const productId = row.querySelector(`select[name$='.ProductId']`)?.value;
        const product = this.products?.$values?.find(x => x.Id == productId);
        const units = product?.ProductUnits?.$values;

        const selectedUnit = units?.find(x => x.ProductUnitId == unitId);
        if (selectedUnit) {
            inventoryTools.updateProductUnitRatio(index, selectedUnit);
        }
    }

    onQuantityChanged(element) {
        if (this.isSales) {
            inventoryTools.updateSalesAmount(element);
        } else {
            inventoryTools.updateCostAmount(element);
        }
    }

    addProductRow(transactionTypeId) {
        inventoryTools.addRow({
            transactionType: transactionTypeId,
            products: this.products,
            stores: this.stores,
            primaryKeyName: this.primaryKeyName,
            isSales: this.isSales,
            modelName: "Inventories",
            tableId: "InventoriesDataGridView",
            includeHidden: this.includeHidden,
            hiddenValues: this.includeHidden && this.hiddenValue ? { ProductId: this.hiddenValue.value } : null
        });
    }

    addTaxRow(includeHidden = false, hiddenValue = null) {
        taxTools.addRow({
            taxTypes: this.taxTypes,
            taxSubTypes: this.taxSubTypes,
            modelName: "InventoryTaxes",
            tableId: "InventoryTaxesDataGridView",
            handlerName: "inventoryHandler",
            isInventory: true,
            includeHidden,
            hiddenValue: hiddenValue ? new TaxValueModel(hiddenValue) : null
        });
    }

    onTaxTypeChanged(taxTypeId, index) {
        const filteredType = this.taxTypes?.$values?.find(t => t.Id == taxTypeId);
        const subTypes = this.taxSubTypes?.$values?.filter(s => s.TaxTypeId == taxTypeId) || [];
        const isAddition = filteredType?.IsAddition ?? true;

        // Determine which table we're working with based on whether bulk tax table exists and has rows
        let tableId = "InventoryTaxesDataGridView";
        const bulkTaxTable = document.getElementById("BulkTaxesDataGridView");
        if (bulkTaxTable && bulkTaxTable.querySelector(`tr[data-row-index="${index}"]`)) {
            tableId = "BulkTaxesDataGridView";
        }

        taxTools.updateTaxSubTypeDropdown(tableId, index, subTypes, isAddition);
    }

    calculateTaxAmount(element) {
        taxTools.calculateTaxAmount(element);
    }

    onOpenSelectTaxType(index) {
        this.selectedRowIndex = index;

        const tableBody = document.querySelector("#InventoryTaxesDataGridView tbody");
        if (!tableBody) return;
        tableBody.innerHTML = "";

        let existingTaxes = [];
        const targetRow = document.querySelector(`#InventoriesDataGridView tr[data-row-index="${index}"]`);
        const hiddenInput = targetRow?.querySelector(`input[name="Inventories[${index}].InventoryTaxesJson"]`);

        if (hiddenInput?.value) {
            try {
                existingTaxes = JSON.parse(hiddenInput.value);
            } catch {
                existingTaxes = [];
            }
        }

        existingTaxes.forEach(tax => this.addTaxRow(true, tax));

        const modalElement = document.getElementById("taxModal");
        if (!modalElement) return;

        const taxModal = new bootstrap.Modal(modalElement);
        taxModal.show();

        const onModalHidden = () => {
            document.body.style.overflowY = '';
            document.body.style.paddingRight = '';
            modalElement.removeEventListener('hidden.bs.modal', onModalHidden);
        };
        modalElement.addEventListener('hidden.bs.modal', onModalHidden);
    }

    confirmTaxSelection() {
        const rows = document.querySelectorAll("#InventoryTaxesDataGridView tbody tr");
        const selectedTaxes = [];
        let totalTaxAmount = 0;
        let inventoryId = '';

        const row = document.querySelector(`#InventoriesDataGridView tr[data-row-index="${this.selectedRowIndex}"]`);
        inventoryId = row.querySelector(`input[type='hidden'][name*='Id']`)?.value || '';

        rows.forEach(row => {
            const taxTypeId = row.querySelector(`select[name$='.TaxTypeId']`)?.value;
            const taxSubTypeId = row.querySelector(`select[name$='.TaxSubTypeId']`)?.value;
            const ratio = parseFloat(row.querySelector(`input[name$='.InventoryTaxsRatio']`)?.value) || 0;
            const amount = parseFloat(row.querySelector(`input[name$='.InventoryTaxsAmount']`)?.value) || 0;

            const tax = this.taxTypes?.$values?.find(t => t.Id == taxTypeId);
            if (tax) {
                totalTaxAmount += tax.IsAddition ? amount : -amount;

                if (taxTypeId && taxSubTypeId) {
                    selectedTaxes.push({
                        InventoryId: inventoryId,
                        IsAddition: tax.IsAddition,
                        TaxTypeId: taxTypeId,
                        TaxSubTypeId: taxSubTypeId,
                        InventoryTaxsRatio: ratio,
                        InventoryTaxsAmount: amount
                    });
                }
            }
        });

        const taxInput = row?.querySelector(`input[name="Inventories[${this.selectedRowIndex}].TaxAmount"]`);
        if (taxInput) {
            taxInput.value = totalTaxAmount.toFixed(2);
        }

        const hiddenInput = row?.querySelector(`input[name="Inventories[${this.selectedRowIndex}].InventoryTaxesJson"]`);
        if (hiddenInput) {
            hiddenInput.value = JSON.stringify(selectedTaxes);
        }

        this.setSelectedTaxes(this.selectedRowIndex, selectedTaxes);
        this.closeTaxSelection();
    }

    closeTaxSelection() {
        const modalElement = document.getElementById("taxModal");
        if (!modalElement) return;

        const modal = bootstrap.Modal.getInstance(modalElement) || new bootstrap.Modal(modalElement);
        modal.hide();

        const onModalHidden = () => {
            document.body.style.overflowY = 'auto';
            document.body.classList.remove('modal-open');

            const backdrop = document.querySelector('.modal-backdrop');
            if (backdrop) backdrop.remove();

            modalElement.removeEventListener('hidden.bs.modal', onModalHidden);
        };
        modalElement.addEventListener('hidden.bs.modal', onModalHidden);
    }

    setSelectedTaxes(rowIndex, selectedTaxes) {
        const row = document.querySelector(`#InventoriesDataGridView tr[data-row-index="${rowIndex}"]`);
        if (!row) return;

        // Remove existing tax inputs (except JSON)
        row.querySelectorAll("input[name*='InventoryTaxes']").forEach(el => {
            if (!el.name.includes('InventoryTaxesJson')) el.remove();
        });

        // Update the JSON hidden input
        const hiddenInput = row.querySelector(`input[name="Inventories[${rowIndex}].InventoryTaxesJson"]`);
        if (hiddenInput) {
            hiddenInput.value = JSON.stringify(selectedTaxes);
        }

        // Add individual tax inputs
        selectedTaxes.forEach((tax, i) => {
            const fields = [
                { name: 'InventoryId', value: tax.InventoryId },
                { name: 'TaxTypeId', value: tax.TaxTypeId },
                { name: 'TaxSubTypeId', value: tax.TaxSubTypeId },
                { name: 'InventoryTaxsRatio', value: tax.InventoryTaxsRatio },
                { name: 'InventoryTaxsAmount', value: tax.InventoryTaxsAmount },
            ];

            fields.forEach(field => {
                const input = document.createElement("input");
                input.type = "hidden";
                input.name = `Inventories[${rowIndex}].InventoryTaxes[${i}].${field.name}`;
                input.value = field.value;
                row.appendChild(input);
            });
        });

        // Recalculate amounts
        const quantityInput = row.querySelector(`input[name$='.Quantity']`);
        if (quantityInput) {
            this.onQuantityChanged(quantityInput);
        }
    }

    // Set linked transaction IDs for a specific row
    setLinkedTransactionIds(rowIndex, linkedTransactionId, linkedTransactionLineId) {
        const row = document.querySelector(`#InventoriesDataGridView tr[data-row-index="${rowIndex}"]`);
        if (!row) return;

        const linkedTransactionIdInput = row.querySelector(`input[name="Inventories[${rowIndex}].LinkedTransactionId"]`);
        const linkedTransactionLineIdInput = row.querySelector(`input[name="Inventories[${rowIndex}].LinkedTransactionLineId"]`);
        
        if (linkedTransactionIdInput) {
            linkedTransactionIdInput.value = linkedTransactionId || '';
        }
        if (linkedTransactionLineIdInput) {
            linkedTransactionLineIdInput.value = linkedTransactionLineId || '';
        }
    }

    // Check if an order line already exists in the inventory grid
    isOrderLineInInventory(linkedTransactionId, linkedTransactionLineId) {
        return inventoryTools.checkLinkedTransactionLineExists(linkedTransactionId, linkedTransactionLineId);
    }

    // Get all linked transaction IDs from the inventory grid
    getLinkedTransactionIds() {
        const linkedIds = [];
        const rows = document.querySelectorAll('#InventoriesDataGridView tbody tr');
        
        rows.forEach(row => {
            const linkedTransactionId = row.querySelector(`input[name$='.LinkedTransactionId']`)?.value;
            const linkedTransactionLineId = row.querySelector(`input[name$='.LinkedTransactionLineId']`)?.value;
            
            if (linkedTransactionId && linkedTransactionLineId) {
                linkedIds.push({
                    linkedTransactionId: linkedTransactionId,
                    linkedTransactionLineId: linkedTransactionLineId
                });
            }
        });
        
        return linkedIds;
    }

    addInventoryItemFromSalesOrderLine(salesOrderLine, index) {
        // Validate that this order line hasn't been added already
        const linkedTransactionId = salesOrderLine.SalesOrderId || salesOrderLine.Id;
        const linkedTransactionLineId = salesOrderLine.Id;
        
        if (!inventoryTools.validateOrderLineSelection(linkedTransactionId, linkedTransactionLineId, inventoryTools.TRANSACTION_TYPE_IDS.SALES_ORDER)) {
            return false; // Duplicate detected, don't add
        }

        // Add a new inventory row
        this.addProductRow("01JRKA5XSQSFE4H3R3M177EDN5"); // Invoice transaction type

        // Get the newly added row
        const rows = document.querySelectorAll('#InventoriesDataGridView tbody tr');
        const newRow = rows[rows.length - 1];
        const rowIndex = newRow.getAttribute('data-row-index');

        // Populate the row with sales order line data
        if (newRow) {
            // Set linked transaction IDs
            const linkedTransactionIdInput = newRow.querySelector(`input[name="Inventories[${rowIndex}].LinkedTransactionId"]`);
            const linkedTransactionLineIdInput = newRow.querySelector(`input[name="Inventories[${rowIndex}].LinkedTransactionLineId"]`);
            
            if (linkedTransactionIdInput) {
                linkedTransactionIdInput.value = linkedTransactionId;
            }
            if (linkedTransactionLineIdInput) {
                linkedTransactionLineIdInput.value = linkedTransactionLineId;
            }

            // Set product
            const productSelect = newRow.querySelector(`select[name="Inventories[${rowIndex}].ProductId"]`);
            if (productSelect && salesOrderLine.ProductId) {
                productSelect.value = salesOrderLine.ProductId;
                this.onProductChanged(salesOrderLine.ProductId, rowIndex);
            }

            // Set unit
            const unitSelect = newRow.querySelector(`select[name="Inventories[${rowIndex}].ProductUnitId"]`);
            if (unitSelect && salesOrderLine.ProductUnitId) {
                unitSelect.value = salesOrderLine.ProductUnitId;
                this.onUnitChanged(salesOrderLine.ProductUnitId, rowIndex);
            }

            // Set quantity
            const quantityInput = newRow.querySelector(`input[name="Inventories[${rowIndex}].Quantity"]`);
            if (quantityInput && salesOrderLine.Quantity) {
                quantityInput.value = salesOrderLine.Quantity;
            }

            // Set sales price (use the price from sales order line)
            const salesPriceInput = newRow.querySelector(`input[name="Inventories[${rowIndex}].SalesPrice"]`);
            if (salesPriceInput && salesOrderLine.Price) {
                salesPriceInput.value = salesOrderLine.Price;
            }

            // Set is percentage discount (use the is percentage discount from sales order line)
            const isPercentageDiscountInput = newRow.querySelector(`input[name="Inventories[${rowIndex}].IsPercentageDiscount"]`);
            if (isPercentageDiscountInput && salesOrderLine.IsPercentageDiscount) {
                isPercentageDiscountInput.value = salesOrderLine.IsPercentageDiscount;
            }

            // Set discount rate (use the discount rate from sales order line)
            const discountRateInput = newRow.querySelector(`input[name="Inventories[${rowIndex}].DiscountRate"]`);
            if (discountRateInput && salesOrderLine.DiscountRate) {
                discountRateInput.value = salesOrderLine.DiscountRate;
            }

            // Set discount amount (use the discount amount from sales order line)
            const discountAmountInput = newRow.querySelector(`input[name="Inventories[${rowIndex}].DiscountAmount"]`);
            if (discountAmountInput && salesOrderLine.DiscountAmount) {
                discountAmountInput.value = salesOrderLine.DiscountAmount;
            }

            // Set unit quantity ratio
            const unitQtyRatioInput = newRow.querySelector(`input[name="Inventories[${rowIndex}].UnitQtyRatio"]`);
            if (unitQtyRatioInput && salesOrderLine.UnitQtyRatio) {
                unitQtyRatioInput.value = salesOrderLine.UnitQtyRatio;
            }

            // Set store (use first available store if not specified)
            const storeSelect = newRow.querySelector(`select[name="Inventories[${rowIndex}].StoreId"]`);
            if (storeSelect && this.stores && this.stores.length > 0) {
                storeSelect.value = this.stores[0].Id;
            }

            // Calculate amounts
            if (quantityInput) {
                this.onQuantityChanged(quantityInput);
            }
        }
        
        return true; // Successfully added
    }

    addInventoryItemFromPurchaseOrderLine(purchaseOrderLine, index) {
        // Validate that this order line hasn't been added already
        const linkedTransactionId = purchaseOrderLine.PurchaseOrderId || purchaseOrderLine.Id;
        const linkedTransactionLineId = purchaseOrderLine.Id;
        
        if (!inventoryTools.validateOrderLineSelection(linkedTransactionId, linkedTransactionLineId, inventoryTools.TRANSACTION_TYPE_IDS.PURCHASE_ORDER)) {
            return false; // Duplicate detected, don't add
        }

        // Add a new inventory row
        this.addProductRow("01JRKA5XSNHEQWKTR0NCJWYC6W"); // Bill transaction type

        // Get the newly added row
        const rows = document.querySelectorAll('#InventoriesDataGridView tbody tr');
        const newRow = rows[rows.length - 1];
        const rowIndex = newRow.getAttribute('data-row-index');

        // Populate the row with purchase order line data
        if (newRow) {
            // Set linked transaction IDs
            const linkedTransactionIdInput = newRow.querySelector(`input[name="Inventories[${rowIndex}].LinkedTransactionId"]`);
            const linkedTransactionLineIdInput = newRow.querySelector(`input[name="Inventories[${rowIndex}].LinkedTransactionLineId"]`);
            
            if (linkedTransactionIdInput) {
                linkedTransactionIdInput.value = linkedTransactionId;
            }
            if (linkedTransactionLineIdInput) {
                linkedTransactionLineIdInput.value = linkedTransactionLineId;
            }

            // Set product
            const productSelect = newRow.querySelector(`select[name="Inventories[${rowIndex}].ProductId"]`);
            if (productSelect && purchaseOrderLine.ProductId) {
                productSelect.value = purchaseOrderLine.ProductId;
                this.onProductChanged(purchaseOrderLine.ProductId, rowIndex);
            }

            // Set unit
            const unitSelect = newRow.querySelector(`select[name="Inventories[${rowIndex}].ProductUnitId"]`);
            if (unitSelect && purchaseOrderLine.ProductUnitId) {
                unitSelect.value = purchaseOrderLine.ProductUnitId;
                this.onUnitChanged(purchaseOrderLine.ProductUnitId, rowIndex);
            }

            // Set quantity
            const quantityInput = newRow.querySelector(`input[name="Inventories[${rowIndex}].Quantity"]`);
            if (quantityInput && purchaseOrderLine.Quantity) {
                quantityInput.value = purchaseOrderLine.Quantity;
            }

            // Set purchase price (use the price from purchase order line)
            const purchasePriceInput = newRow.querySelector(`input[name="Inventories[${rowIndex}].CostPrice"]`);
            if (purchasePriceInput && purchaseOrderLine.Price) {
                purchasePriceInput.value = purchaseOrderLine.Price;
            }

            // Set is percentage discount (use the is percentage discount from sales order line)
            const isPercentageDiscountInput = newRow.querySelector(`input[name="Inventories[${rowIndex}].IsPercentageDiscount"]`);
            if (isPercentageDiscountInput && purchaseOrderLine.IsPercentageDiscount) {
                isPercentageDiscountInput.value = purchaseOrderLine.IsPercentageDiscount;
            }

            // Set discount rate (use the discount rate from sales order line)
            const discountRateInput = newRow.querySelector(`input[name="Inventories[${rowIndex}].DiscountRate"]`);
            if (discountRateInput && purchaseOrderLine.DiscountRate) {
                discountRateInput.value = purchaseOrderLine.DiscountRate;
            }

            // Set discount amount (use the discount amount from sales order line)
            const discountAmountInput = newRow.querySelector(`input[name="Inventories[${rowIndex}].DiscountAmount"]`);
            if (discountAmountInput && purchaseOrderLine.DiscountAmount) {
                discountAmountInput.value = purchaseOrderLine.DiscountAmount;
            }

            // Set unit quantity ratio
            const unitQtyRatioInput = newRow.querySelector(`input[name="Inventories[${rowIndex}].UnitQtyRatio"]`);
            if (unitQtyRatioInput && purchaseOrderLine.UnitQtyRatio) {
                unitQtyRatioInput.value = purchaseOrderLine.UnitQtyRatio;
            }

            // Set store (use first available store if not specified)
            const storeSelect = newRow.querySelector(`select[name="Inventories[${rowIndex}].StoreId"]`);
            if (storeSelect && this.stores && this.stores.length > 0) {
                storeSelect.value = this.stores[0].Id;
            }

            // Calculate amounts
            if (quantityInput) {
                this.onQuantityChanged(quantityInput);
            }
        }
        
        return true; // Successfully added
    }

    onOpenSelectDiscount(index) {
        this.selectedRowIndex = index;

        // Get the row and current values
        const row = document.querySelector(`#InventoriesDataGridView tr[data-row-index="${index}"]`);
        if (!row) return;

        // Populate modal fields
        const productName = row.querySelector(`select[name$='.ProductId']`)?.selectedOptions[0]?.textContent || '';
        const qty = row.querySelector(`input[name$='.Quantity']`)?.value || '';
        const price = row.querySelector(`input[name$='.SalesPrice']`)?.value || row.querySelector(`input[name$='.CostPrice']`)?.value || '';
        const isPercentage = row.querySelector(`input[name$='.IsPercentageDiscount']`)?.value.toLowerCase() === 'true';
        const discountRate = row.querySelector(`input[name$='.DiscountRate']`)?.value || '0';
        const discountAmount = row.querySelector(`input[name$='.DiscountAmount']`)?.value || '0';

        document.getElementById('discountModalProductName').textContent = productName;
        document.getElementById('discountModalQty').textContent = qty;
        document.getElementById('discountModalPrice').textContent = price;
        document.getElementById('discountModalIsPercentage').checked = isPercentage;
        document.getElementById('discountModalRate').value = discountRate;
        document.getElementById('discountModalAmount').value = discountAmount;

        // Show modal
        const modalElement = document.getElementById('discountModal');
        if (!modalElement) return;
        const discountModal = new bootstrap.Modal(modalElement);
        discountModal.show();

        const onModalHidden = () => {
            document.body.style.overflowY = '';
            document.body.style.paddingRight = '';
            modalElement.removeEventListener('hidden.bs.modal', onModalHidden);
        };
        modalElement.addEventListener('hidden.bs.modal', onModalHidden);
   }

    confirmDiscountSelection() {
        const index = this.selectedRowIndex;
        const row = document.querySelector(`#InventoriesDataGridView tr[data-row-index="${index}"]`);
        if (!row) return;

        const isPercentage = document.getElementById('discountModalIsPercentage').checked;
        const discountRate = parseFloat(document.getElementById('discountModalRate').value) || 0;
        const discountAmount = parseFloat(document.getElementById('discountModalAmount').value) || 0;

        // Set hidden fields
        row.querySelector(`input[name="Inventories[${index}].IsPercentageDiscount"]`).value = isPercentage;
        row.querySelector(`input[name="Inventories[${index}].DiscountRate"]`).value = discountRate;
        row.querySelector(`input[name="Inventories[${index}].DiscountAmount"]`).value = discountAmount;

        // Recalculate row
        const quantityInput = row.querySelector(`input[name$='.Quantity']`);
        if (quantityInput)
            this.onQuantityChanged(quantityInput);

        // Close modal
        this.closeDiscountSelection();
    }

    closeDiscountSelection() {
        const modalElement = document.getElementById("discountModal");
        if (!modalElement) return;

        const modal = bootstrap.Modal.getInstance(modalElement) || new bootstrap.Modal(modalElement);
        modal.hide();

        const onModalHidden = () => {
            document.body.style.overflowY = 'auto';
            document.body.classList.remove('modal-open');

            const backdrop = document.querySelector('.modal-backdrop');
            if (backdrop) backdrop.remove();

            modalElement.removeEventListener('hidden.bs.modal', onModalHidden);
        };
        modalElement.addEventListener('hidden.bs.modal', onModalHidden);
    }

    onIsPercentageChanged() {
        const isPercentageElement = document.getElementById('discountModalIsPercentage');
        const discountModalRateElement = document.getElementById('discountModalRate');
        const discountModalAmountElement = document.getElementById('discountModalAmount');
        if (!isPercentageElement || !discountModalRateElement || !discountModalAmountElement)
            return;
        if (isPercentageElement.checked === true) {
            discountModalRateElement.readOnly = false;
            discountModalAmountElement.readOnly = true;
        } else {
            discountModalRateElement.readOnly = true;
            discountModalAmountElement.readOnly = false;
        }
    }

    onDiscountChanged() {
        const isPercentageElement = document.getElementById('discountModalIsPercentage');
        const discountModalRateElement = document.getElementById('discountModalRate');
        const discountModalAmountElement = document.getElementById('discountModalAmount');
        if (!isPercentageElement || !discountModalRateElement || !discountModalAmountElement)
            return;
        const index = this.selectedRowIndex;
        const row = document.querySelector(`#InventoriesDataGridView tr[data-row-index="${index}"]`);
        if (!row) return;
        const qty = row.querySelector(`input[name$='.Quantity']`)?.value || '';
        const price = row.querySelector(`input[name$='.SalesPrice']`)?.value || row.querySelector(`input[name$='.CostPrice']`)?.value || '';
        if (isPercentageElement.checked === true) {
            discountModalAmountElement.value = ((parseFloat(discountModalRateElement.value) / 100) * price * qty) || 0;;
        } else {
            discountModalRateElement.value = ((price * qty) / parseFloat(discountModalAmountElement.value)) || 0;;
        }
    }

    // Bulk Tax Application Methods
    onOpenBulkTaxSelection() {
        const tableBody = document.querySelector("#BulkTaxesDataGridView tbody");
        if (!tableBody) return;
        tableBody.innerHTML = "";

        // Show modal
        const modalElement = document.getElementById("bulkTaxModal");
        if (!modalElement) return;

        const bulkTaxModal = new bootstrap.Modal(modalElement);
        bulkTaxModal.show();

        const onModalHidden = () => {
            document.body.style.overflowY = '';
            document.body.style.paddingRight = '';
            modalElement.removeEventListener('hidden.bs.modal', onModalHidden);
        };
        modalElement.addEventListener('hidden.bs.modal', onModalHidden);
    }

    addBulkTaxRow() {
        taxTools.addRow({
            taxTypes: this.taxTypes,
            taxSubTypes: this.taxSubTypes,
            modelName: "BulkTaxes",
            tableId: "BulkTaxesDataGridView",
            handlerName: "inventoryHandler",
            isInventory: false,
            includeHidden: false,
            hiddenValue: null
        });
    }

    onBulkTaxTypeChanged(taxTypeId, index) {
        const filteredType = this.taxTypes?.$values?.find(t => t.Id == taxTypeId);
        const subTypes = this.taxSubTypes?.$values?.filter(s => s.TaxTypeId == taxTypeId) || [];
        const isAddition = filteredType?.IsAddition ?? true;

        taxTools.updateTaxSubTypeDropdown("BulkTaxesDataGridView", index, subTypes, isAddition);
    }

    confirmBulkTaxSelection() {
        const rows = document.querySelectorAll("#BulkTaxesDataGridView tbody tr");
        const selectedTaxes = [];

        rows.forEach(row => {
            const taxTypeId = row.querySelector(`select[name$='.TaxTypeId']`)?.value;
            const taxSubTypeId = row.querySelector(`select[name$='.TaxSubTypeId']`)?.value;
            const ratio = parseFloat(row.querySelector(`input[name$='.ProductTaxsRatio']`)?.value) || 0;

            const tax = this.taxTypes?.$values?.find(t => t.Id == taxTypeId);
            if (tax && taxTypeId && taxSubTypeId && ratio > 0) {
                selectedTaxes.push({
                    TaxTypeId: taxTypeId,
                    TaxSubTypeId: taxSubTypeId,
                    InventoryTaxsRatio: ratio,
                    IsAddition: tax.IsAddition
                });
            }
        });

        if (selectedTaxes.length > 0) {
            this.applyBulkTaxesToAllItems(selectedTaxes);
        }

        this.closeBulkTaxSelection();
    }

    applyBulkTaxesToAllItems(selectedTaxes) {
        const inventoryRows = document.querySelectorAll('#InventoriesDataGridView tbody tr');

        inventoryRows.forEach((row) => {
            const rowIndex = row.getAttribute('data-row-index');
            if (!rowIndex) return;

            // Get current taxes
            const hiddenInput = row.querySelector(`input[name="Inventories[${rowIndex}].InventoryTaxesJson"]`);
            let existingTaxes = [];

            if (hiddenInput?.value) {
                try {
                    existingTaxes = JSON.parse(hiddenInput.value);
                } catch {
                    existingTaxes = [];
                }
            }

            // Get inventory ID
            const inventoryId = row.querySelector(`input[type='hidden'][name*='Id']`)?.value || '';

            // Add new taxes to existing ones
            selectedTaxes.forEach(newTax => {
                // Check if this tax type/subtype combination already exists
                const existingTaxIndex = existingTaxes.findIndex(t =>
                    t.TaxTypeId == newTax.TaxTypeId && t.TaxSubTypeId == newTax.TaxSubTypeId
                );

                if (existingTaxIndex >= 0) {
                    // Update existing tax
                    existingTaxes[existingTaxIndex].InventoryTaxsRatio = newTax.InventoryTaxsRatio;
                    existingTaxes[existingTaxIndex].IsAddition = newTax.IsAddition;
                } else {
                    // Add new tax
                    existingTaxes.push({
                        InventoryId: inventoryId,
                        TaxTypeId: newTax.TaxTypeId,
                        TaxSubTypeId: newTax.TaxSubTypeId,
                        InventoryTaxsRatio: newTax.InventoryTaxsRatio,
                        InventoryTaxsAmount: 0, // Will be calculated
                        IsAddition: newTax.IsAddition
                    });
                }
            });

            // Update the row with new taxes and recalculate amounts
            this.setSelectedTaxes(rowIndex, existingTaxes);
        });
    }

    closeBulkTaxSelection() {
        const modalElement = document.getElementById("bulkTaxModal");
        if (!modalElement) return;

        const modal = bootstrap.Modal.getInstance(modalElement) || new bootstrap.Modal(modalElement);
        modal.hide();

        const onModalHidden = () => {
            // Clear the bulk tax table
            const bulkTaxTable = document.getElementById("BulkTaxesDataGridView");
            if (bulkTaxTable) {
                const tbody = bulkTaxTable.querySelector("tbody");
                if (tbody) {
                    tbody.innerHTML = "";
                }
            }

            document.body.style.overflowY = 'auto';
            document.body.classList.remove('modal-open');

            const backdrop = document.querySelector('.modal-backdrop');
            if (backdrop) backdrop.remove();

            modalElement.removeEventListener('hidden.bs.modal', onModalHidden);
        };
        modalElement.addEventListener('hidden.bs.modal', onModalHidden);
    }

    // Bulk Discount Application Methods
    onOpenBulkDiscountSelection() {
        // Reset modal fields
        document.getElementById('bulkDiscountModalIsPercentage').checked = true;
        document.getElementById('bulkDiscountModalRate').value = '0';
        document.getElementById('bulkDiscountModalAmount').value = '0';

        // Show modal
        const modalElement = document.getElementById('bulkDiscountModal');
        if (!modalElement) return;
        const bulkDiscountModal = new bootstrap.Modal(modalElement);
        bulkDiscountModal.show();

        const onModalHidden = () => {
            document.body.style.overflowY = '';
            document.body.style.paddingRight = '';
            modalElement.removeEventListener('hidden.bs.modal', onModalHidden);
        };
        modalElement.addEventListener('hidden.bs.modal', onModalHidden);
    }

    confirmBulkDiscountSelection() {
        const isPercentage = document.getElementById('bulkDiscountModalIsPercentage').checked;
        const discountRate = parseFloat(document.getElementById('bulkDiscountModalRate').value) || 0;
        const discountAmount = parseFloat(document.getElementById('bulkDiscountModalAmount').value) || 0;

        this.applyBulkDiscountToAllItems(isPercentage, discountRate, discountAmount);
        this.closeBulkDiscountSelection();
    }

    applyBulkDiscountToAllItems(isPercentage, discountRate, discountAmount) {
        const inventoryRows = document.querySelectorAll('#InventoriesDataGridView tbody tr');

        inventoryRows.forEach((row) => {
            const rowIndex = row.getAttribute('data-row-index');
            if (!rowIndex) return;

            // Set discount fields
            const isPercentageInput = row.querySelector(`input[name="Inventories[${rowIndex}].IsPercentageDiscount"]`);
            const discountRateInput = row.querySelector(`input[name="Inventories[${rowIndex}].DiscountRate"]`);
            const discountAmountInput = row.querySelector(`input[name="Inventories[${rowIndex}].DiscountAmount"]`);

            if (isPercentageInput && discountRateInput && discountAmountInput) {
                isPercentageInput.value = isPercentage;
                discountRateInput.value = discountRate;

                if (isPercentage) {
                    // Calculate discount amount based on percentage
                    const qty = parseFloat(row.querySelector(`input[name$='.Quantity']`)?.value) || 0;
                    const price = parseFloat(row.querySelector(`input[name$='.SalesPrice']`)?.value || row.querySelector(`input[name$='.CostPrice']`)?.value) || 0;
                    const calculatedDiscountAmount = (discountRate / 100) * price * qty;
                    discountAmountInput.value = calculatedDiscountAmount.toFixed(2);
                } else {
                    discountAmountInput.value = discountAmount.toFixed(2);
                }

                // Recalculate row amounts
                const quantityInput = row.querySelector(`input[name$='.Quantity']`);
                if (quantityInput) {
                    this.onQuantityChanged(quantityInput);
                }
            }
        });
    }

    closeBulkDiscountSelection() {
        const modalElement = document.getElementById("bulkDiscountModal");
        if (!modalElement) return;

        const modal = bootstrap.Modal.getInstance(modalElement) || new bootstrap.Modal(modalElement);
        modal.hide();

        const onModalHidden = () => {
            document.body.style.overflowY = 'auto';
            document.body.classList.remove('modal-open');

            const backdrop = document.querySelector('.modal-backdrop');
            if (backdrop) backdrop.remove();

            modalElement.removeEventListener('hidden.bs.modal', onModalHidden);
        };
        modalElement.addEventListener('hidden.bs.modal', onModalHidden);
    }

    onBulkDiscountIsPercentageChanged() {
        const isPercentageElement = document.getElementById('bulkDiscountModalIsPercentage');
        const discountModalRateElement = document.getElementById('bulkDiscountModalRate');
        const discountModalAmountElement = document.getElementById('bulkDiscountModalAmount');

        if (!isPercentageElement || !discountModalRateElement || !discountModalAmountElement)
            return;

        if (isPercentageElement.checked === true) {
            discountModalRateElement.readOnly = false;
            discountModalAmountElement.readOnly = true;
            discountModalAmountElement.value = '0';
        } else {
            discountModalRateElement.readOnly = true;
            discountModalAmountElement.readOnly = false;
            discountModalRateElement.value = '0';
        }
    }
}
