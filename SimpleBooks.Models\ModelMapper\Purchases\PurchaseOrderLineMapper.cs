﻿namespace SimpleBooks.Models.ModelMapper.Purchases
{
    public static class PurchaseOrderLineMapper
    {
        public static CreatePurchaseOrderLineViewModel ToCreateDto(this PurchaseOrderLineModel entity)
        {
            CreatePurchaseOrderLineViewModel viewModel = new CreatePurchaseOrderLineViewModel()
            {
                Quantity = entity.Quantity,
                UnitQtyRatio = entity.UnitQtyRatio,
                Price = entity.Price,
                IsPercentageDiscount = entity.IsPercentageDiscount,
                DiscountRate = entity.DiscountRate,
                DiscountAmount = entity.DiscountAmount,
                Amount = entity.Amount,
                ProductId = entity.ProductId,
                ProductUnitId = entity.ProductUnitId,
                PurchaseOrderId = entity.PurchaseOrderId,
            };
            return viewModel;
        }

        public static PurchaseOrderLineModel ToEntity(this CreatePurchaseOrderLineViewModel entity)
        {
            PurchaseOrderLineModel model = new PurchaseOrderLineModel()
            {
                Quantity = entity.Quantity,
                UnitQtyRatio = entity.UnitQtyRatio,
                Price = entity.Price,
                IsPercentageDiscount = entity.IsPercentageDiscount,
                DiscountRate = entity.DiscountRate,
                DiscountAmount = entity.DiscountAmount,
                Amount = entity.Amount,
                ProductId = entity.ProductId,
                ProductUnitId = entity.ProductUnitId,
                PurchaseOrderId = entity.PurchaseOrderId,
            };
            return model;
        }

        public static UpdatePurchaseOrderLineViewModel ToUpdateDto(this PurchaseOrderLineModel entity)
        {
            UpdatePurchaseOrderLineViewModel viewModel = new UpdatePurchaseOrderLineViewModel()
            {
                Id = entity.Id,
                Quantity = entity.Quantity,
                UnitQtyRatio = entity.UnitQtyRatio,
                Price = entity.Price,
                IsPercentageDiscount = entity.IsPercentageDiscount,
                DiscountRate = entity.DiscountRate,
                DiscountAmount = entity.DiscountAmount,
                Amount = entity.Amount,
                ProductId = entity.ProductId,
                ProductUnitId = entity.ProductUnitId,
                PurchaseOrderId = entity.PurchaseOrderId,
            };
            return viewModel;
        }

        public static PurchaseOrderLineModel ToEntity(this UpdatePurchaseOrderLineViewModel entity)
        {
            PurchaseOrderLineModel model = new PurchaseOrderLineModel()
            {
                Id = entity.Id,
                Quantity = entity.Quantity,
                UnitQtyRatio = entity.UnitQtyRatio,
                Price = entity.Price,
                IsPercentageDiscount = entity.IsPercentageDiscount,
                DiscountRate = entity.DiscountRate,
                DiscountAmount = entity.DiscountAmount,
                Amount = entity.Amount,
                ProductId = entity.ProductId,
                ProductUnitId = entity.ProductUnitId,
                PurchaseOrderId = entity.PurchaseOrderId,
            };
            return model;
        }
    }
}
