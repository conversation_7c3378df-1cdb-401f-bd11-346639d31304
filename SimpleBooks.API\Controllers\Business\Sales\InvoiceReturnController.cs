﻿namespace SimpleBooks.API.Controllers.Business.Sales
{
    public class InvoiceReturnController : BaseBusinessController<InvoiceReturnModel, IndexInvoiceReturnViewModel, CreateInvoiceReturnViewModel, UpdateInvoiceReturnViewModel>
    {
        private readonly IInvoiceReturnService _invoiceReturnService;

        public InvoiceReturnController(IInvoiceReturnService invoiceReturnService) : base(invoiceReturnService)
        {
            _invoiceReturnService = invoiceReturnService;
        }

        [HttpGet(nameof(GetReportInvoiceReturnViewModelAsync))]
        public async Task<IActionResult> GetReportInvoiceReturnViewModelAsync(string id)
        {
            try
            {
                Ulid ulid = Ulid.Parse(id);
                var result = await _invoiceReturnService.GetReportInvoiceReturnViewModelAsync(ulid);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }
    }
}
