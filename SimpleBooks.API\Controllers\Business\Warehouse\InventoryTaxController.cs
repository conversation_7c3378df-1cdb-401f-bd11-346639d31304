﻿namespace SimpleBooks.API.Controllers.Business.Warehouse
{
    public class InventoryTaxController : BaseBusinessController<InventoryTaxModel, InventoryTaxModel, CreateInventoryTaxViewModel, UpdateInventoryTaxViewModel>
    {
        private readonly IInventoryTaxService _productTaxService;

        public InventoryTaxController(IInventoryTaxService productTaxService) : base(productTaxService)
        {
            _productTaxService = productTaxService;
        }
    }
}
