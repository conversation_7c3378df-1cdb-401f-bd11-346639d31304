﻿namespace SimpleBooks.API.Controllers.Business.Treasury
{
    public class PaymentTermController : BaseBusinessController<PaymentTermModel, PaymentTermModel, CreatePaymentTermViewModel, UpdatePaymentTermViewModel>
    {
        private readonly IPaymentTermService _paymentTermService;

        public PaymentTermController(IPaymentTermService paymentTermService) : base(paymentTermService)
        {
            _paymentTermService = paymentTermService;
        }

        [HttpPost("AddStandardAsync")]
        public async Task<IActionResult> AddAsync([FromBody] CreatePaymentTermStandardViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                    return BadRequest(new { Message = "Invalid data submitted.", ModelState = ModelState });

                var result = await _paymentTermService.AddStandardAsync(model);

                if (result is null)
                    return BadRequest(new { Message = "Failed to create entity. Please check your data and try again." });

                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPut("UpdateStandardAsync")]
        public async Task<IActionResult> UpdateAsync([FromBody] UpdatePaymentTermStandardViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                    return BadRequest(new { Message = "Invalid data submitted.", ModelState = ModelState });

                var result = await _paymentTermService.UpdateStandardAsync(model);

                if (result is null)
                    return BadRequest(new { Message = "Failed to update entity. Please check your data and try again." });

                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPost("AddDateDrivenAsync")]
        public async Task<IActionResult> AddAsync([FromBody] CreatePaymentTermDateDrivenViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                    return BadRequest(new { Message = "Invalid data submitted.", ModelState = ModelState });

                var result = await _paymentTermService.AddDateDrivenAsync(model);

                if (result is null)
                    return BadRequest(new { Message = "Failed to create entity. Please check your data and try again." });

                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPut("UpdateDateDrivenAsync")]
        public async Task<IActionResult> UpdateAsync([FromBody] UpdatePaymentTermDateDrivenViewModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                    return BadRequest(new { Message = "Invalid data submitted.", ModelState = ModelState });

                var result = await _paymentTermService.UpdateDateDrivenAsync(model);

                if (result is null)
                    return BadRequest(new { Message = "Failed to update entity. Please check your data and try again." });

                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet(nameof(SelectivePaymentTermDtoListAsync))]
        public async Task<IActionResult> SelectivePaymentTermDtoListAsync()
        {
            try
            {
                var result = await _paymentTermService.SelectivePaymentTermDtoListAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet(nameof(GetByIdJsonAsync))]
        public async Task<IActionResult> GetByIdJsonAsync(string id)
        {
            try
            {
                Ulid ulid = Ulid.Parse(id);
                var result = await _paymentTermService.GetByIdJsonAsync(ulid);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
