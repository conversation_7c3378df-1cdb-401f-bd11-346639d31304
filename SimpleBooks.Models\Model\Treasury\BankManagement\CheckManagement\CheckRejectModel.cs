﻿namespace SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement
{
    [Table("CheckReject")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<CheckRejectModel>))]
    public partial class CheckRejectModel : BaseModel
    {
        [CustomRequired]
        [DisplayName("Reject Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateTime RejectDate { get; set; }
        [CustomRequired]
        [DisplayName("Reject Reason")]
        public string RejectReason { get; set; }
        [DisplayName("Refrance Number")]
        public string? RefranceNumber { get; set; }

        [CustomRequired]
        [DisplayName("Check Treasury Vouchers")]
        public virtual ICollection<CheckTreasuryVoucherModel> CheckTreasuryVouchers { get; set; } = new List<CheckTreasuryVoucherModel>();
        [DisplayName("Check Status Histories")]
        public virtual ICollection<CheckStatusHistoryModel> CheckStatusHistories { get; set; } = new List<CheckStatusHistoryModel>();
    }
}
