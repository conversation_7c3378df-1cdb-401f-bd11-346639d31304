﻿namespace SimpleBooks.Models.Model.Tax
{
    [Table("TaxType")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<TaxTypeModel>))]
    public class TaxTypeModel : BaseWithoutTrackingModel
    {
        [CustomRequired]
        [DisplayName("Code")]
        public string Code { get; set; }
        [CustomRequired]
        [DisplayName("En")]
        public string Desc_en { get; set; }
        [CustomRequired]
        [DisplayName("Ar")]
        public string Desc_ar { get; set; }
        [CustomRequired]
        [DisplayName("Is Addition")]
        public bool IsAddition { get; set; }

        public virtual ICollection<TaxSubTypeModel> TaxSubTypes { get; set; } = new List<TaxSubTypeModel>();
        public virtual ICollection<ProductTaxModel> ProductTaxes { get; set; } = new List<ProductTaxModel>();
        public virtual ICollection<InventoryTaxModel> InventoryTaxes { get; set; } = new List<InventoryTaxModel>();
    }
}
