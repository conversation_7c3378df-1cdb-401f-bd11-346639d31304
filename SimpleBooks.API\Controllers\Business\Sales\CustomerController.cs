﻿namespace SimpleBooks.API.Controllers.Business.Sales
{
    public class CustomerController : BaseBusinessController<CustomerModel, IndexCustomerViewModel, CreateCustomerViewModel, UpdateCustomerViewModel>
    {
        private readonly ICustomerService _customerService;

        public CustomerController(ICustomerService customerService) : base(customerService)
        {
            _customerService = customerService;
        }

        [HttpGet(nameof(SelectiveCustomerDtoListAsync))]
        public async Task<IActionResult> SelectiveCustomerDtoListAsync()
        {
            try
            {
                var result = await _customerService.SelectiveCustomerDtoListAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
