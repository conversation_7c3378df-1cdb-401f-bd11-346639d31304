﻿namespace SimpleBooks.Models.JsonConverters
{
    public class PaymentTermConverter : JsonConverter
    {
        public override bool CanConvert(Type objectType)
        {
            return typeof(PaymentTermModel).IsAssignableFrom(objectType);
        }

        public override object? ReadJson(JsonReader reader, Type objectType, object? existingValue, JsonSerializer serializer)
        {
            JObject jsonObject = JObject.Load(reader);

            if (
                jsonObject.ContainsKey(nameof(PaymentTermStandardModel.NetDueDays)) &&
                jsonObject.ContainsKey(nameof(PaymentTermStandardModel.IfPaidWithinDays)))
            {
                return new PaymentTermStandardModel
                {
                    Id = ValidateValue.ValidateUlid(jsonObject[nameof(PaymentTermStandardModel.Id)]?.ToString()),
                    PaymentTermName = ValidateValue.ValidateString(jsonObject[nameof(PaymentTermStandardModel.PaymentTermName)]?.ToString()),
                    DiscountPercentage = ValidateValue.ValidateInt(jsonObject[nameof(PaymentTermStandardModel.DiscountPercentage)]?.ToObject<int>()),
                    NetDueDays = ValidateValue.ValidateInt(jsonObject[nameof(PaymentTermStandardModel.NetDueDays)]?.ToObject<int>()),
                    IfPaidWithinDays = ValidateValue.ValidateInt(jsonObject[nameof(PaymentTermStandardModel.IfPaidWithinDays)]?.ToObject<int>()),
                };
            }
            else if (
                jsonObject.ContainsKey(nameof(PaymentTermDateDrivenModel.NetDueBeforeDayOfMonth)) &&
                jsonObject.ContainsKey(nameof(PaymentTermDateDrivenModel.DueNextMonthWithinDays)) &&
                jsonObject.ContainsKey(nameof(PaymentTermDateDrivenModel.IfPaidBeforeDayOfMonth)))
            {
                return new PaymentTermDateDrivenModel
                {
                    Id = ValidateValue.ValidateUlid(jsonObject[nameof(PaymentTermDateDrivenModel.Id)]?.ToString()),
                    PaymentTermName = ValidateValue.ValidateString(jsonObject[nameof(PaymentTermDateDrivenModel.PaymentTermName)]?.ToString()),
                    DiscountPercentage = ValidateValue.ValidateInt(jsonObject[nameof(PaymentTermDateDrivenModel.DiscountPercentage)]?.ToObject<int>()),
                    NetDueBeforeDayOfMonth = ValidateValue.ValidateInt(jsonObject[nameof(PaymentTermDateDrivenModel.NetDueBeforeDayOfMonth)]?.ToObject<int>()),
                    DueNextMonthWithinDays = ValidateValue.ValidateInt(jsonObject[nameof(PaymentTermDateDrivenModel.DueNextMonthWithinDays)]?.ToObject<int>()),
                    IfPaidBeforeDayOfMonth = ValidateValue.ValidateInt(jsonObject[nameof(PaymentTermDateDrivenModel.IfPaidBeforeDayOfMonth)]?.ToObject<int>()),
                };
            }

            throw new InvalidOperationException("Unknown payment term type.");
        }

        public override void WriteJson(JsonWriter writer, object? value, JsonSerializer serializer)
        {
            if (value == null)
            {
                writer.WriteNull();
                return;
            }
            JObject jsonObject = JObject.FromObject(value);
            jsonObject.WriteTo(writer);
        }
    }
}
