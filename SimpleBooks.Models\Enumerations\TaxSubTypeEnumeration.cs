﻿namespace SimpleBooks.Models.Enumerations
{
    public class TaxSubTypeEnumeration : UlidEnumeration<TaxSubTypeEnumeration>
    {
        public static readonly TaxSubTypeEnumeration V001 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMJHZ2R4W9AXTZA6766"), "V001", "Export", "تصدير للخارج", TaxTypeEnumeration.T1);
        public static readonly TaxSubTypeEnumeration V002 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMJYW8C4QAVZH5MFST5"), "V002", "Export to free areas and other areas", "تصدير مناطق حرة وأخرى", TaxTypeEnumeration.T1);
        public static readonly TaxSubTypeEnumeration V003 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMJDGQ75J7RBKNTSTQF"), "V003", "Exempted good or service", "سلعة أو خدمة معفاة", TaxTypeEnumeration.T1);
        public static readonly TaxSubTypeEnumeration V004 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMJN95XY34QFPWNEH0S"), "V004", "A non-taxable good or service", "سلعة أو خدمة غير خاضعة للضريبة", TaxTypeEnumeration.T1);
        public static readonly TaxSubTypeEnumeration V005 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMJ3NA115CBHHXNJMNY"), "V005", "Exemptions for diplomats, consulates and embassies", "إعفاءات دبلوماسين والقنصليات والسفارات", TaxTypeEnumeration.T1);
        public static readonly TaxSubTypeEnumeration V006 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMJ1YSCTA2H6T6SED3P"), "V006", "Defence and National security Exemptions", "إعفاءات الدفاع والأمن القومى", TaxTypeEnumeration.T1);
        public static readonly TaxSubTypeEnumeration V007 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMKQRZFEB9ETRAP0VQZ"), "V007", "Agreements exemptions", "إعفاءات اتفاقيات", TaxTypeEnumeration.T1);
        public static readonly TaxSubTypeEnumeration V008 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMKVC7SSTYYN08KHRAP"), "V008", "Special Exemptios and other reasons", "إعفاءات خاصة و أخرى", TaxTypeEnumeration.T1);
        public static readonly TaxSubTypeEnumeration V009 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMK6V5HH0W4PNZRZTKV"), "V009", "General Item sales", "سلع عامة", TaxTypeEnumeration.T1);
        public static readonly TaxSubTypeEnumeration V010 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMKSXCH3YEM40NV096J"), "V010", "Other Rates", "نسب ضريبة أخرى", TaxTypeEnumeration.T1);
        public static readonly TaxSubTypeEnumeration Tbl01 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMKTA96NHMZAGVDY0E6"), "Tbl01", "Table tax (percentage)", "ضريبه الجدول (نسبيه)", TaxTypeEnumeration.T2);
        public static readonly TaxSubTypeEnumeration Tbl02 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMKCS6EH5C8G1KT33FC"), "Tbl02", "Table tax (Fixed Amount)", "ضريبه الجدول (النوعية)", TaxTypeEnumeration.T3);
        public static readonly TaxSubTypeEnumeration W001 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMK16XJXNQC94B8P1P3"), "W001", "Contracting", "المقاولات", TaxTypeEnumeration.T4);
        public static readonly TaxSubTypeEnumeration W002 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMKWJB4PXVWBX0139BV"), "W002", "Supplies", "التوريدات", TaxTypeEnumeration.T4);
        public static readonly TaxSubTypeEnumeration W003 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMKBWWK3D8TX38V7ZPC"), "W003", "Purachases", "المشتريات", TaxTypeEnumeration.T4);
        public static readonly TaxSubTypeEnumeration W004 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMKKTWSK3JFV867PDGR"), "W004", "Services", "الخدمات", TaxTypeEnumeration.T4);
        public static readonly TaxSubTypeEnumeration W005 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMKMCDVD7FRD6BVANG4"), "W005", "Sumspaid by the cooperative societies for car transportation to their members", "المبالغالتي تدفعها الجميعات التعاونية للنقل بالسيارات لاعضائها", TaxTypeEnumeration.T4);
        public static readonly TaxSubTypeEnumeration W006 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMKVBZ51PD3AN1PA1ZA"), "W006", "Commissionagency & brokerage", "الوكالةبالعمولة والسمسرة", TaxTypeEnumeration.T4);
        public static readonly TaxSubTypeEnumeration W007 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMKXYYQP6SKY6ZSA30E"), "W007", "Discounts& grants & additional exceptional incentives granted by smoke &cement companies", "الخصوماتوالمنح والحوافز الاستثنائية ةالاضافية التي تمنحها شركات الدخان والاسمنت ", TaxTypeEnumeration.T4);
        public static readonly TaxSubTypeEnumeration W008 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMKD21BYKY0WSHCVT4G"), "W008", "Alldiscounts & grants & commissions granted by petroleum &telecommunications & other companies", "جميعالخصومات والمنح والعمولات  التيتمنحها  شركات البترول والاتصالات ...وغيرها من الشركات المخاطبة بنظام الخصم", TaxTypeEnumeration.T4);
        public static readonly TaxSubTypeEnumeration W009 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMMK5Y5624NNFY3CM2X"), "W009", "Supporting export subsidies", "مساندة دعم الصادرات التي يمنحها صندوق تنمية الصادرات ", TaxTypeEnumeration.T4);
        public static readonly TaxSubTypeEnumeration W010 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMMT81BZZFVSR758YVM"), "W010", "Professional fees", "اتعاب مهنية", TaxTypeEnumeration.T4);
        public static readonly TaxSubTypeEnumeration W011 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMMQQANZQHGBAQZ74ZV"), "W011", "Commission & brokerage _A_57", "العمولة والسمسرة _م_57", TaxTypeEnumeration.T4);
        public static readonly TaxSubTypeEnumeration W012 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMM5NDW2Q2BVR60JV3H"), "W012", "Hospitals collecting from doctors", "تحصيل المستشفيات من الاطباء", TaxTypeEnumeration.T4);
        public static readonly TaxSubTypeEnumeration W013 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMMHDGXE64KP2PH6EE4"), "W013", "Royalties", "الاتاوات", TaxTypeEnumeration.T4);
        public static readonly TaxSubTypeEnumeration W014 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMMPGBKG2JDHFYWS9FH"), "W014", "Customs clearance", "تخليص جمركي ", TaxTypeEnumeration.T4);
        public static readonly TaxSubTypeEnumeration W015 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMMD545W0VC5HPP6CMH"), "W015", "Exemption", "أعفاء", TaxTypeEnumeration.T4);
        public static readonly TaxSubTypeEnumeration W016 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMMA4AMVGMZQHMK19WF"), "W016", "advance payments", "دفعات مقدمه", TaxTypeEnumeration.T4);
        public static readonly TaxSubTypeEnumeration ST01 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMMYAWRRQTZG75XKZ68"), "ST01", "Stamping tax (percentage)", "ضريبه الدمغه (نسبيه)", TaxTypeEnumeration.T5);
        public static readonly TaxSubTypeEnumeration ST02 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMM43A145PBRYW0A8CH"), "ST02", "Stamping Tax (amount)", "ضريبه الدمغه (قطعيه بمقدار ثابت)", TaxTypeEnumeration.T6);
        public static readonly TaxSubTypeEnumeration Ent01 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMMKPEKR0H72HVAXK3R"), "Ent01", "Entertainment tax (rate)", "ضريبة الملاهى (نسبة)", TaxTypeEnumeration.T7);
        public static readonly TaxSubTypeEnumeration Ent02 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMM6Y65XSCXD951QW26"), "Ent02", "Entertainment tax (amount)", "ضريبة الملاهى (قطعية)", TaxTypeEnumeration.T7);
        public static readonly TaxSubTypeEnumeration RD01 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMM67M7BNBJ9FAPR43J"), "RD01", "Resource development fee (rate)", "رسم تنميه الموارد (نسبة)", TaxTypeEnumeration.T8);
        public static readonly TaxSubTypeEnumeration RD02 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMM3ABBYMMX9GWY5T60"), "RD02", "Resource development fee (amount)", "رسم تنميه الموارد (قطعية)", TaxTypeEnumeration.T8);
        public static readonly TaxSubTypeEnumeration SC01 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMMFYB9TP9SARKH30HY"), "SC01", "Service charges (rate)", "رسم خدمة (نسبة)", TaxTypeEnumeration.T9);
        public static readonly TaxSubTypeEnumeration SC02 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMMAP568B5A5CQQ78K4"), "SC02", "Service charges (amount)", "رسم خدمة (قطعية)", TaxTypeEnumeration.T9);
        public static readonly TaxSubTypeEnumeration Mn01 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6KEMNDB4GR4N4NMH112ES"), "Mn01", "Municipality Fees (rate)", "رسم المحليات (نسبة)", TaxTypeEnumeration.T10);
        public static readonly TaxSubTypeEnumeration Mn02 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6R5RVF7YCQ9H08A9ATJYH"), "Mn02", "Municipality Fees (amount)", "رسم المحليات (قطعية)", TaxTypeEnumeration.T10);
        public static readonly TaxSubTypeEnumeration MI01 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6R5RVRQQGXHVN4RRAM98G"), "MI01", "Medical insurance fee (rate)", "رسم التامين الصحى (نسبة)", TaxTypeEnumeration.T11);
        public static readonly TaxSubTypeEnumeration MI02 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6R5RVAZ79A391959E97ZY"), "MI02", "Medical insurance fee (amount)", "رسم التامين الصحى (قطعية)", TaxTypeEnumeration.T11);
        public static readonly TaxSubTypeEnumeration OF01 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6R5RVY6G76MCFE14P5F9X"), "OF01", "Other fees (rate)", "رسوم أخرى (نسبة)", TaxTypeEnumeration.T12);
        public static readonly TaxSubTypeEnumeration OF02 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6R5RVRMKR8A43X519YQ7Y"), "OF02", "Other fees (amount)", "رسوم أخرى (قطعية)", TaxTypeEnumeration.T12);
        public static readonly TaxSubTypeEnumeration ST03 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6R5RV78GJ7RVTYF1R71PD"), "ST03", "Stamping tax (percentage)", "ضريبه الدمغه (نسبيه)", TaxTypeEnumeration.T13);
        public static readonly TaxSubTypeEnumeration ST04 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6R5RVXDKMWNBE94H65ES3"), "ST04", "Stamping Tax (amount)", "ضريبه الدمغه (قطعيه بمقدار ثابت)", TaxTypeEnumeration.T14);
        public static readonly TaxSubTypeEnumeration Ent03 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6R5RWR5ZT5NTWW5CNCKTT"), "Ent03", "Entertainment tax (rate)", "ضريبة الملاهى (نسبة)", TaxTypeEnumeration.T15);
        public static readonly TaxSubTypeEnumeration Ent04 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6R5RWGVAV02Q9T02G4CH6"), "Ent04", "Entertainment tax (amount)", "ضريبة الملاهى (قطعية)", TaxTypeEnumeration.T15);
        public static readonly TaxSubTypeEnumeration RD03 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6R5RWXJNBXBPHW42EKGPV"), "RD03", "Resource development fee (rate)", "رسم تنميه الموارد (نسبة)", TaxTypeEnumeration.T16);
        public static readonly TaxSubTypeEnumeration RD04 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6R5RWRA4Q22AGM4CR283T"), "RD04", "Resource development fee (amount)", "رسم تنميه الموارد (قطعية)", TaxTypeEnumeration.T16);
        public static readonly TaxSubTypeEnumeration SC03 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6R5RWR4NE3D53E1T1MYPS"), "SC03", "Service charges (rate)", "رسم خدمة (نسبة)", TaxTypeEnumeration.T17);
        public static readonly TaxSubTypeEnumeration SC04 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6R5RW4NEPJXPJT8TF4SV2"), "SC04", "Service charges (amount)", "رسم خدمة (قطعية)", TaxTypeEnumeration.T17);
        public static readonly TaxSubTypeEnumeration Mn03 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6R5RW1CAGGTV477HF59AD"), "Mn03", "Municipality Fees (rate)", "رسم المحليات (نسبة)", TaxTypeEnumeration.T18);
        public static readonly TaxSubTypeEnumeration Mn04 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6R5RW49QW3MNMS8XG2T9M"), "Mn04", "Municipality Fees (amount)", "رسم المحليات (قطعية)", TaxTypeEnumeration.T18);
        public static readonly TaxSubTypeEnumeration MI03 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6R5RWKSP3T79AGWKFSJ4E"), "MI03", "Medical insurance fee (rate)", "رسم التامين الصحى (نسبة)", TaxTypeEnumeration.T19);
        public static readonly TaxSubTypeEnumeration MI04 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6R5RW4TE80A5E1DPND9SE"), "MI04", "Medical insurance fee (amount)", "رسم التامين الصحى (قطعية)", TaxTypeEnumeration.T19);
        public static readonly TaxSubTypeEnumeration OF03 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6R5RWK548QE6RKDV8NKXZ"), "OF03", "Other fees (rate)", "رسوم أخرى (نسبة)", TaxTypeEnumeration.T20);
        public static readonly TaxSubTypeEnumeration OF04 = new TaxSubTypeEnumeration(Ulid.Parse("01JRN6R5RW16W2TCJTEXPB22FH"), "OF04", "Other fees (amount)", "رسوم أخرى (قطعية)", TaxTypeEnumeration.T20);

        private TaxSubTypeEnumeration(Ulid key, string value, string desc_en, string desc_ar, TaxTypeEnumeration taxTypeEnumeration) : base(key, value)
        {
            Desc_en = desc_en;
            Desc_ar = desc_ar;
            TaxTypeEnumeration = taxTypeEnumeration;
        }


        public string Desc_en { get; }
        public string Desc_ar { get; }
        public TaxTypeEnumeration TaxTypeEnumeration { get; }

        public static List<TaxSubTypeEnumeration> TaxSubTypeEnumerations
        {
            get => new List<TaxSubTypeEnumeration>
            {
                V001, V002, V003, V004, V005, V006, V007, V008, V009, V010,
                Tbl01, Tbl02,
                W001, W002, W003, W004, W005, W006, W007, W008, W009, W010,
                W011, W012, W013, W014, W015, W016,
                ST01, ST02,
                Ent01, Ent02,
                RD01, RD02,
                SC01, SC02,
                Mn01, Mn02,
                MI01, MI02,
                OF01, OF02,
                ST03, ST04,
                Ent03, Ent04,
                RD03, RD04,
                SC03, SC04,
                Mn03, Mn04,
                MI03, MI04,
                OF03, OF04
                };
        }
    }
}
