﻿namespace SimpleBooks.Models.Enumerations
{
    public class CheckStatusEnumeration : UlidEnumeration<CheckStatusEnumeration>
    {
        public static readonly CheckStatusEnumeration Unknown = new CheckStatusEnumeration(Ulid.Parse("01JZXN7EBKTG5YX238JNT92KFD"), "Unknown");
        public static readonly CheckStatusEnumeration Issued = new CheckStatusEnumeration(Ulid.Parse("01JZBRJWHRNQTHR1MJ693B2STZ"), "Issued");
        public static readonly CheckStatusEnumeration Cleared = new CheckStatusEnumeration(Ulid.Parse("01JZBRK6P4GNTX4097YAKT3ZRZ"), "Cleared");
        public static readonly CheckStatusEnumeration Received = new CheckStatusEnumeration(Ulid.Parse("01JZBRK149KQWD0TJ971CQ5WC1"), "Received");
        public static readonly CheckStatusEnumeration Deposited = new CheckStatusEnumeration(Ulid.Parse("01JZBRK3ZGQPTB0S7RNN8QC8EA"), "Deposited");
        public static readonly CheckStatusEnumeration Collected = new CheckStatusEnumeration(Ulid.Parse("01K0J1KKN1YZMJYQ86F7C4JMS4"), "Collected");
        public static readonly CheckStatusEnumeration Rejected = new CheckStatusEnumeration(Ulid.Parse("01JZBRKD187QRR8QSB7SWJQ4TM"), "Rejected");
        public static readonly CheckStatusEnumeration Returned = new CheckStatusEnumeration(Ulid.Parse("01JZBRKA75CZZW5P4ARW47NY4P"), "Returned");

        private CheckStatusEnumeration(Ulid key, string value) : base(key, value)
        {
        }

        public static List<CheckStatusEnumeration> CheckStatusEnumerations
        {
            get => new List<CheckStatusEnumeration>
            {
                Unknown, Issued, Cleared, Received, Deposited, Collected, Rejected, Returned,
            };
        }

        public static bool IsInitialCheckStatus(Ulid key)
        {
            return
                key == Issued.Value ||
                key == Received.Value ||
                key == Unknown.Value;
        }

        public static Ulid InitialCheckStatus(Ulid transactionTypeId)
        {
            return
                transactionTypeId == TransactionTypeEnumeration.TreasuryCheckOut.Value ? Issued.Value :
                transactionTypeId == TransactionTypeEnumeration.TreasuryCheckIn.Value ? Received.Value :
                Unknown.Value;
        }
    }
}
