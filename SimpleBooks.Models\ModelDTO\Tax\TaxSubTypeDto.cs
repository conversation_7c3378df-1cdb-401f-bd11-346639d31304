﻿namespace SimpleBooks.Models.ModelDTO.Tax
{
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<TaxSubTypeDto>))]
    public class TaxSubTypeDto : BaseWithoutTrackingModel
    {
        public string Code { get; set; }
        public string Desc_en { get; set; }
        public string Desc_ar { get; set; }

        public string SelectedText => $"{Code} - {Desc_en}";

        public Ulid TaxTypeId { get; set; }
    }
}
