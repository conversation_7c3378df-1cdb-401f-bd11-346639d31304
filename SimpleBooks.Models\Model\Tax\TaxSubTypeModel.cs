﻿namespace SimpleBooks.Models.Model.Tax
{
    [Table("TaxSubType")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<TaxSubTypeModel>))]
    public class TaxSubTypeModel : BaseWithoutTrackingModel
    {
        [CustomRequired]
        [DisplayName("Code")]
        public string Code { get; set; }
        [CustomRequired]
        [DisplayName("En")]
        public string Desc_en { get; set; }
        [CustomRequired]
        [DisplayName("Ar")]
        public string Desc_ar { get; set; }

        [DisplayName("Tax Type")]
        public Ulid TaxTypeId { get; set; }
        public virtual TaxTypeModel? TaxType { get; set; }

        public virtual ICollection<ProductTaxModel> ProductTaxes { get; set; } = new List<ProductTaxModel>();
        public virtual ICollection<InventoryTaxModel> InventoryTaxes { get; set; } = new List<InventoryTaxModel>();
    }
}
