﻿namespace SimpleBooks.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AuthenticationController : ControllerBase
    {
        private readonly IAuthenticationService _authenticationService;

        public AuthenticationController(IAuthenticationService authenticationService)
        {
            _authenticationService = authenticationService;
        }

        [HttpPost("LoginAsync")]
        public async Task<IActionResult> LoginAsync([FromBody] LoginRequest model)
        {
            try
            {
                var result = await _authenticationService.LoginAsync(model);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPost("RefreshTokenAsync")]
        public async Task<IActionResult> RefreshTokenAsync([FromBody] RefreshTokenRequest model)
        {
            try
            {
                var result = await _authenticationService.RefreshTokenAsync(model);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }

        }

        [HttpGet("RevokeTokenAsync")]
        public async Task<IActionResult> RevokeTokenAsync(string revokeToken)
        {
            var token = revokeToken ?? Request.Cookies[AuthenticationDefaults.RefreshTokenCookie];

            if (string.IsNullOrEmpty(token))
                return BadRequest("Token is required!");

            try
            {
                var result = await _authenticationService.RevokeTokenAsync(token);
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }

        }
    }
}
