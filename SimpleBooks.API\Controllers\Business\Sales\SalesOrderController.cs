﻿namespace SimpleBooks.API.Controllers.Business.Sales
{
    public class SalesOrderController : BaseBusinessController<SalesOrderModel, IndexSalesOrderViewModel, CreateSalesOrderViewModel, UpdateSalesOrderViewModel>
    {
        private readonly ISalesOrderService _salesOrderService;

        public SalesOrderController(ISalesOrderService salesOrderService) : base(salesOrderService)
        {
            _salesOrderService = salesOrderService;
        }

        [HttpGet(nameof(GetInvoicedQuantitiesAsync))]
        public async Task<IActionResult> GetInvoicedQuantitiesAsync(string salesOrderId)
        {
            try
            {
                Ulid ulid = Ulid.Parse(salesOrderId);
                var result = await _salesOrderService.GetInvoicedQuantitiesAsync(ulid);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet(nameof(GetInvoiceDetailsByOrderAndProductAsync))]
        public async Task<IActionResult> GetInvoiceDetailsByOrderAndProductAsync(string salesOrderId, string productId)
        {
            try
            {
                Ulid ulid = Ulid.Parse(salesOrderId);
                Ulid ulid2 = Ulid.Parse(productId);
                var result = await _salesOrderService.GetInvoiceDetailsByOrderAndProductAsync(ulid, ulid2);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet(nameof(GetReportSalesOrderViewModelAsync))]
        public async Task<IActionResult> GetReportSalesOrderViewModelAsync(string id)
        {
            try
            {
                Ulid ulid = Ulid.Parse(id);
                var result = await _salesOrderService.GetReportSalesOrderViewModelAsync(ulid);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }
    }
}
