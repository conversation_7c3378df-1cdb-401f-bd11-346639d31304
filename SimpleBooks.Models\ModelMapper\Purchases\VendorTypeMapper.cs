﻿namespace SimpleBooks.Models.ModelMapper.Purchases
{
    public static class VendorTypeMapper
    {
        public static CreateVendorTypeViewModel ToCreateDto(this VendorTypeModel entity)
        {
            CreateVendorTypeViewModel viewModel = new CreateVendorTypeViewModel()
            {
                VendorTypeName = entity.VendorTypeName,
            };
            return viewModel;
        }

        public static VendorTypeModel ToEntity(this CreateVendorTypeViewModel entity)
        {
            VendorTypeModel model = new VendorTypeModel()
            {
                VendorTypeName = entity.VendorTypeName,
            };
            return model;
        }

        public static UpdateVendorTypeViewModel ToUpdateDto(this VendorTypeModel entity)
        {
            UpdateVendorTypeViewModel viewModel = new UpdateVendorTypeViewModel()
            {
                Id = entity.Id,
                VendorTypeName = entity.VendorTypeName,
            };
            return viewModel;
        }

        public static VendorTypeModel ToEntity(this UpdateVendorTypeViewModel entity)
        {
            VendorTypeModel model = new VendorTypeModel()
            {
                Id = entity.Id,
                VendorTypeName = entity.VendorTypeName,
            };
            return model;
        }
    }
}
