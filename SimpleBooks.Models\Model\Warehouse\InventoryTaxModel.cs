﻿namespace SimpleBooks.Models.Model.Warehouse
{
    [Table("InventoryProductTax")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<InventoryTaxModel>))]
    public class InventoryTaxModel : BaseWithoutTrackingModel
    {
        [CustomRequired]
        [DisplayName("Inventory Taxs Ratio")]
        [Range(0, 1)]
        public decimal InventoryTaxsRatio { get; set; }

        [CustomRequired]
        [DisplayName("Inventory Taxs Amount")]
        public decimal InventoryTaxsAmount { get; set; }

        [CustomRequired]
        [DisplayName("Inventory")]
        public Ulid InventoryId { get; set; }
        public virtual InventoryModel? Inventory { get; set; }

        [CustomRequired]
        [DisplayName("Tax Type")]
        public Ulid TaxTypeId { get; set; }
        public virtual TaxTypeModel? TaxType { get; set; }

        [CustomRequired]
        [DisplayName("Tax SubType")]
        public Ulid TaxSubTypeId { get; set; }
        public virtual TaxSubTypeModel? TaxSubType { get; set; }
    }
}
