﻿namespace SimpleBooks.Models.ModelDTO.Sales
{
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<OpenSalesOrderDto>))]
    public class OpenSalesOrderDto
    {
        public Ulid Id { get; set; }
        public string SalesOrderId { get; set; }
        public DateOnly SalesOrderDate { get; set; }
        public DateOnly SalesOrderDueDate { get; set; }
        public Ulid CustomerId { get; set; }
        public Ulid? CustomerTypeId { get; set; }
        public Ulid? CustomerRepId { get; set; }
        public Ulid? PaymentTermId { get; set; }
        public virtual ICollection<OpenSalesOrderLineDto> OpenSalesOrderLines { get; set; } = new List<OpenSalesOrderLineDto>();
    }

    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<OpenSalesOrderLineDto>))]
    public class OpenSalesOrderLineDto
    {
        public Ulid Id { get; set; }
        public decimal Quantity { get; set; }
        public decimal Price { get; set; }
        public bool IsPercentageDiscount { get; set; }
        public decimal DiscountRate { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal Amount { get; set; }
        public decimal UnitQtyRatio { get; set; }
        public Ulid ProductId { get; set; }
        public Ulid ProductUnitId { get; set; }
        public Ulid SalesOrderId { get; set; }
    }
}
