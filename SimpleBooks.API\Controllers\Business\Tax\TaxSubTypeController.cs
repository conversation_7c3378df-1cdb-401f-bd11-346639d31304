﻿namespace SimpleBooks.API.Controllers.Business.Tax
{
    public class TaxSubTypeController : BaseBusinessController<TaxSubTypeModel, TaxSubTypeModel, CreateTaxSubTypeViewModel, UpdateTaxSubTypeViewModel>
    {
        private readonly ITaxSubTypeService _taxSubTypeService;

        public TaxSubTypeController(ITaxSubTypeService taxSubTypeService) : base(taxSubTypeService)
        {
            _taxSubTypeService = taxSubTypeService;
        }

        [HttpGet(nameof(SelectiveTaxSubTypeDtoListAsync))]
        public async Task<IActionResult> SelectiveTaxSubTypeDtoListAsync()
        {
            try
            {
                var result = await _taxSubTypeService.SelectiveTaxSubTypeDtoListAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
