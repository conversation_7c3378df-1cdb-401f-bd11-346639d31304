﻿namespace SimpleBooks.Models.ModelDTO.Tax
{
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<TaxTypeDto>))]
    public class TaxTypeDto : BaseWithoutTrackingModel
    {
        public string Code { get; set; }
        public string Desc_en { get; set; }
        public string Desc_ar { get; set; }
        public bool IsAddition { get; set; }

        public string SelectedText => $"{Code} - {Desc_en}";

        public List<TaxSubTypeDto> TaxSubTypes { get; set; } = new List<TaxSubTypeDto>();
    }
}
