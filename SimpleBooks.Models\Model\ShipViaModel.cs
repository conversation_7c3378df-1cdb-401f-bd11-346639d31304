﻿namespace SimpleBooks.Models.Model
{
    [Table("ShipVia")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<ShipViaModel>))]
    public class ShipViaModel : BaseWithoutTrackingModel
    {
        [CustomRequired]
        [DisplayName("Ship Via")]
        public string ShipViaName { get; set; }

        [DisplayName("Customers")]
        public virtual ICollection<CustomerModel> Customers { get; set; } = new List<CustomerModel>();
        [DisplayName("Invoices")]
        public virtual ICollection<InvoiceModel> Invoices { get; set; } = new List<InvoiceModel>();
        [DisplayName("Invoice Returns")]
        public virtual ICollection<InvoiceReturnModel> InvoiceReturns { get; set; } = new List<InvoiceReturnModel>();
        [DisplayName("Sales Orders")]
        public virtual ICollection<SalesOrderModel> SalesOrders { get; set; } = new List<SalesOrderModel>();
        [DisplayName("Vendors")]
        public virtual ICollection<VendorModel> Vendors { get; set; } = new List<VendorModel>();
        [DisplayName("Bills")]
        public virtual ICollection<BillModel> Bills { get; set; } = new List<BillModel>();
        [DisplayName("Bill Returns")]
        public virtual ICollection<BillReturnModel> BillReturns { get; set; } = new List<BillReturnModel>();
        [DisplayName("Purchase Orders")]
        public virtual ICollection<PurchaseOrderModel> PurchaseOrders { get; set; } = new List<PurchaseOrderModel>();
    }
}
