﻿namespace SimpleBooks.Models.ModelDTO.Audit
{
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<AuditEntry>))]
    public class AuditEntry
    {
        public Ulid EmployeeId { get; set; }
        public string TableName { get; set; }
        public Dictionary<string, object?> KeyValues { get; } = new Dictionary<string, object?>();
        public Dictionary<string, object?> OldValues { get; } = new Dictionary<string, object?>();
        public Dictionary<string, object?> NewValues { get; } = new Dictionary<string, object?>();
        public Enums.AuditType AuditType { get; set; }
        public List<string> ChangedColumns { get; } = new List<string>();
        [NotMapped]
        // TempProperties are used for properties that are only generated on save, e.g. ID's
        public List<object> TempProperties { get; set; }
        public AuditModel ToAudit()
        {
            var audit = new AuditModel()
            {
                Type = AuditType.ToString(),
                TableName = TableName,
                DateTime = DateTime.Now,
                PrimaryKey = JsonConvert.SerializeObject(KeyValues),
                OldValues = OldValues.Count == 0 ? "null" : JsonConvert.SerializeObject(OldValues),
                NewValues = NewValues.Count == 0 ? "null" : JsonConvert.SerializeObject(NewValues),
                AffectedColumns = ChangedColumns.Count == 0 ? "null" : JsonConvert.SerializeObject(ChangedColumns),
            };
            return audit;
        }
    }
}
