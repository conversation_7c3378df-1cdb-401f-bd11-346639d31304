namespace SimpleBooks.Models.ModelDTO.Purchases
{
    public class BillDetailsDto
    {
        public Ulid Id { get; set; }
        public string BillId { get; set; } = string.Empty;
        public DateTime BillDate { get; set; }
        public string VendorName { get; set; } = string.Empty;
        public decimal Quantity { get; set; }
        public decimal Price { get; set; }
        public bool IsPercentageDiscount { get; set; }
        public decimal DiscountRate { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal Amount { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public string UnitName { get; set; } = string.Empty;
    }
}
