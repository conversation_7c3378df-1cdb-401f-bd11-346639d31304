﻿namespace SimpleBooks.Models.ModelDTO.Warehouse
{
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<InventoryTaxDto>))]
    public class InventoryTaxDto : BaseWithoutTrackingModel
    {
        public Ulid TaxTypeId { get; set; }
        public Ulid TaxSubTypeId { get; set; }
        public decimal InventoryTaxsRatio { get; set; }
        public decimal InventoryTaxsAmount { get; set; }
        public bool IsAddition { get; set; }
    }
}
