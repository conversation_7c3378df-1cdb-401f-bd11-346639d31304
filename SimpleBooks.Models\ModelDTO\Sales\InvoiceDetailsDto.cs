namespace SimpleBooks.Models.ModelDTO.Sales
{
    public class InvoiceDetailsDto
    {
        public Ulid Id { get; set; }
        public string InvoiceId { get; set; } = string.Empty;
        public DateTime InvoiceDate { get; set; }
        public string CustomerName { get; set; } = string.Empty;
        public decimal Quantity { get; set; }
        public decimal Price { get; set; }
        public bool IsPercentageDiscount { get; set; }
        public decimal DiscountRate { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal Amount { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public string UnitName { get; set; } = string.Empty;
    }
}
