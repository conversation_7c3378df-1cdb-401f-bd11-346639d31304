﻿namespace SimpleBooks.API.Controllers.Business.Tax
{
    public class TaxTypeController : BaseBusinessController<TaxTypeModel, TaxTypeModel, CreateTaxTypeViewModel, UpdateTaxTypeViewModel>
    {
        private readonly ITaxTypeService _taxTypeService;

        public TaxTypeController(ITaxTypeService taxTypeService) : base(taxTypeService)
        {
            _taxTypeService = taxTypeService;
        }

        [HttpGet(nameof(SelectiveTaxTypeDtoListAsync))]
        public async Task<IActionResult> SelectiveTaxTypeDtoListAsync()
        {
            try
            {
                var result = await _taxTypeService.SelectiveTaxTypeDtoListAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
