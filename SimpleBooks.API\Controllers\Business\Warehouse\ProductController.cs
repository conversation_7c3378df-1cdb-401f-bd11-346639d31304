﻿namespace SimpleBooks.API.Controllers.Business.Warehouse
{
    public class ProductController : BaseBusinessController<ProductModel, IndexProductViewModel, CreateProductViewModel, UpdateProductViewModel>
    {
        private readonly IProductService _productService;

        public ProductController(IProductService productService) : base(productService)
        {
            _productService = productService;
        }
    }
}
