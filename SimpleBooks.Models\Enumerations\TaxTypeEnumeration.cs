﻿namespace SimpleBooks.Models.Enumerations
{
    public class TaxTypeEnumeration : UlidEnumeration<TaxTypeEnumeration>
    {
        public static readonly TaxTypeEnumeration T1 = new TaxTypeEnumeration(Ulid.Parse("01JRN6KEMGHX79ZW8H9GPDEF72"), "T1", "Value added tax", "ضريبه القيمه المضافه", true);
        public static readonly TaxTypeEnumeration T2 = new TaxTypeEnumeration(Ulid.Parse("01JRN6KEMHT2B8G7NZVX6J0B7W"), "T2", "Table tax (percentage)", "ضريبه الجدول (نسبيه)", true);
        public static readonly TaxTypeEnumeration T3 = new TaxTypeEnumeration(Ulid.Parse("01JRN6KEMHQ1MEF9FBCDT2DA5S"), "T3", "Table tax (Fixed Amount)", "ضريبه الجدول (قطعيه)", true);
        public static readonly TaxTypeEnumeration T4 = new TaxTypeEnumeration(Ulid.Parse("01JRN6KEMHMNRPK8CTMAZ3XQTK"), "T4", "Withholding tax (WHT)", "الخصم تحت حساب الضريبه", false);
        public static readonly TaxTypeEnumeration T5 = new TaxTypeEnumeration(Ulid.Parse("01JRN6KEMHVHVT0JR971YVPJ92"), "T5", "Stamping tax (percentage)", "ضريبه الدمغه (نسبيه)", true);
        public static readonly TaxTypeEnumeration T6 = new TaxTypeEnumeration(Ulid.Parse("01JRN6KEMJ1FATNAQ3540BR73H"), "T6", "Stamping Tax (amount)", "ضريبه الدمغه (قطعيه بمقدار ثابت )", true);
        public static readonly TaxTypeEnumeration T7 = new TaxTypeEnumeration(Ulid.Parse("01JRN6KEMJCN4J6XCNJGBCSH9D"), "T7", "Entertainment tax", "ضريبة الملاهى", true);
        public static readonly TaxTypeEnumeration T8 = new TaxTypeEnumeration(Ulid.Parse("01JRN6KEMJ7CWC2EZBRBKD2RFJ"), "T8", "Resource development fee", "رسم تنميه الموارد", true);
        public static readonly TaxTypeEnumeration T9 = new TaxTypeEnumeration(Ulid.Parse("01JRN6KEMJHNG5Q0R5AMHSBV85"), "T9", "Table tax (percentage)", "رسم خدمة", true);
        public static readonly TaxTypeEnumeration T10 = new TaxTypeEnumeration(Ulid.Parse("01JRN6KEMJ1EXMTWFQQ8BYP7RS"), "T10", "Municipality Fees", "رسم المحليات", true);
        public static readonly TaxTypeEnumeration T11 = new TaxTypeEnumeration(Ulid.Parse("01JRN6KEMJ36AZH3TSZM1QXXQ2"), "T11", "Medical insurance fee", "رسم التامين الصحى", true);
        public static readonly TaxTypeEnumeration T12 = new TaxTypeEnumeration(Ulid.Parse("01JRN6KEMJ4X5C3NYGDDSZEGEM"), "T12", "Other fees", "رسوم أخري", true);
        public static readonly TaxTypeEnumeration T13 = new TaxTypeEnumeration(Ulid.Parse("01JRN6R5RWY935F174XQM19Y1T"), "T13", "Stamping tax (percentage)", "ضريبه الدمغه (نسبيه)", true);
        public static readonly TaxTypeEnumeration T14 = new TaxTypeEnumeration(Ulid.Parse("01JRN6R5RW3G72VRJ4Q2CA2JTV"), "T14", "Stamping Tax (amount)", "ضريبه الدمغه (قطعيه بمقدار ثابت )", true);
        public static readonly TaxTypeEnumeration T15 = new TaxTypeEnumeration(Ulid.Parse("01JRN6R5RWG7KPJMKBDAHZVHDH"), "T15", "Entertainment tax", "ضريبة الملاهى", true);
        public static readonly TaxTypeEnumeration T16 = new TaxTypeEnumeration(Ulid.Parse("01JRN6R5RW7PD5HBF3563V3JN7"), "T16", "Resource development fee", "رسم تنميه الموارد", true);
        public static readonly TaxTypeEnumeration T17 = new TaxTypeEnumeration(Ulid.Parse("01JRN6R5RWFYWMR74SJCX2G4XN"), "T17", "Table tax (percentage)", "رسم خدمة", true);
        public static readonly TaxTypeEnumeration T18 = new TaxTypeEnumeration(Ulid.Parse("01JRN6R5RWB9NB0XFSY0G9VG4S"), "T18", "Municipality Fees", "رسم المحليات", true);
        public static readonly TaxTypeEnumeration T19 = new TaxTypeEnumeration(Ulid.Parse("01JRN6R5RWET9R49NR7QTZA801"), "T19", "Medical insurance fee", "رسم التامين الصحى", true);
        public static readonly TaxTypeEnumeration T20 = new TaxTypeEnumeration(Ulid.Parse("01JRN6R5RWNEB4PMWHKDY8KEEW"), "T20", "Other fees", "رسوم أخرى", true);

        private TaxTypeEnumeration(Ulid key, string value, string desc_en, string desc_ar, bool isAddition) : base(key, value)
        {
            Desc_en = desc_en;
            Desc_ar = desc_ar;
            IsAddition = isAddition;
        }

        public string Desc_en { get; }
        public string Desc_ar { get; }
        public bool IsAddition { get; }

        public static List<TaxTypeEnumeration> TaxTypeEnumerations
        {
            get => new List<TaxTypeEnumeration>
            {
                T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15, T16, T17, T18, T19, T20
            };
        }
    }
}
