﻿namespace GMCadiomCore.Tools.Helper
{
    public static class Utilities
    {
#if WINDOWS
        public static Image? StringToQRCode(string stringValue)
        {
            if (string.IsNullOrEmpty(stringValue))
                return null;

            QRCodeGenerator Qr = new QRCodeGenerator();
            QRCodeData Data = Qr.CreateQrCode(stringValue, QRCodeGenerator.ECCLevel.L);
            QRCode code = new QRCode(Data);
            Image image = code.GetGraphic(100);
            return image;
        }

        public static BindingList<IdAndName> GetInstalledPrinters()
        {
            var Printers = new BindingList<IdAndName>();
            foreach (string printname in PrinterSettings.InstalledPrinters)
                Printers.Add(new IdAndName() { Id = Ulid.Empty, Name = printname });
            return Printers;
        }

        public static byte[] ImageToByteArray(Image image)
        {
            if (image != null)
            {
                using (MemoryStream memoryStream = new MemoryStream())
                {
                    new Bitmap(image).Save(memoryStream, ImageFormat.Jpeg);
                    return memoryStream.ToArray();
                }
            }
            return Array.Empty<byte>();
        }

        public static Image? ByteArrayToImage(byte[] byteArray)
        {
            if (byteArray?.Length > 0)
            {
                using (MemoryStream memoryStream = new MemoryStream(byteArray, 0, byteArray.Length))
                {
                    memoryStream.Write(byteArray, 0, byteArray.Length);
                    Image returnImage = Image.FromStream(memoryStream, true);
                    return returnImage;
                }
            }
            return null;
        }
#endif

        public static byte[] ImageToByteArray(string imagePath)
        {
            byte[] data = Array.Empty<byte>();
            if (!string.IsNullOrEmpty(imagePath))
            {
                FileInfo fileInfo = new FileInfo(imagePath);
                long numBytes = fileInfo.Length;
                FileStream fileStream = new FileStream(imagePath, FileMode.Open, FileAccess.Read);
                BinaryReader binaryReader = new BinaryReader(fileStream);
                data = binaryReader.ReadBytes((int)numBytes);
                return data;
            }
            return data;
        }

        public static TimeSpan DateTimeDiff(DateTime? startDateTime, DateTime? endDateTime)
        {
            if (startDateTime == null && endDateTime == null)
                return new TimeSpan();

            if (startDateTime == null)
                startDateTime = DateTime.Now;

            if (endDateTime == null)
                endDateTime = DateTime.Now;

            if (startDateTime > endDateTime)
                return (startDateTime - endDateTime).Value;
            else
                return (endDateTime - startDateTime).Value;
        }

        public static bool IsValidEmail(string eMail)
        {
            bool Result = false;
            try
            {
                var eMailValidator = new System.Net.Mail.MailAddress(eMail);

                Result = eMail.LastIndexOf(".") > eMail.LastIndexOf("@");
            }
            catch
            {
                Result = false;
            }

            return Result;
        }

        public static Tuple<string, decimal>? GetProductFromBarCode(string lable)
        {
            Tuple<string, decimal>? tuple = null;
            string ProductId = "";
            decimal kg;
            decimal g;
            decimal Qty;

            if (lable.StartsWith("99"))
            {
                ProductId = lable[2..7];
                kg = ValidateValue.ValidateDecimal(lable[7..9]);
                g = ValidateValue.ValidateDecimal(lable[9..12]);
                Qty = kg + g / 1000;
                tuple = new Tuple<string, decimal>(ProductId, Qty);
            }

            return tuple;
        }

        public static bool? _isInternetAvailable = null;
        public static bool IsInternetAvailable
        {
            get
            {
                if (_isInternetAvailable == null || !_isInternetAvailable.HasValue)
                {
                    using (Ping ping = new Ping())
                    {
                        PingReply reply = ping.Send("www.google.com", 1);
                        _isInternetAvailable = reply.Status == IPStatus.Success;
                    }
                    NetworkChange.NetworkAvailabilityChanged += NetworkChange_NetworkAvailabilityChanged;
                }

                return _isInternetAvailable.Value;
            }
        }

        private static void NetworkChange_NetworkAvailabilityChanged(object? sender, NetworkAvailabilityEventArgs e)
        {
            _isInternetAvailable = e.IsAvailable;
        }
    }
}
