﻿namespace SimpleBooks.Models.Model.Purchases
{
    [Table("BillReturn")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<BillReturnModel>))]
    public class BillReturnModel : BaseWithTrackingModel
    {
        [CustomRequired]
        [DisplayName("Bill Id")]
        public string BillReturnId { get; set; }
        [CustomRequired]
        [DisplayName("Bill Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateOnly BillReturnDate { get; set; }
        [CustomRequired]
        [DisplayName("Bill Due Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateOnly BillReturnDueDate { get; set; }

        [CustomRequired]
        [DisplayName("Vendor")]
        public Ulid VendorId { get; set; }
        public virtual VendorModel? Vendor { get; set; }

        [DisplayName("Vendor Type")]
        public Ulid? VendorTypeId { get; set; }
        public virtual VendorTypeModel? VendorType { get; set; }

        [DisplayName("Payment Term")]
        public Ulid? PaymentTermId { get; set; }
        public virtual PaymentTermModel? PaymentTerm { get; set; }

        [DisplayName("Ship Via")]
        public Ulid? ShipViaId { get; set; }
        public virtual ShipViaModel? ShipVia { get; set; }

        [DisplayName("Delivered By")]
        public Ulid? DeliveredById { get; set; }
        public virtual DeliveredByModel? DeliveredBy { get; set; }

        [CustomRequired]
        [DisplayName("Inventories")]
        public virtual ICollection<InventoryModel> Inventories { get; set; } = new List<InventoryModel>();
        public virtual ICollection<TreasuryLineModel> TreasuryLines { get; set; } = new List<TreasuryLineModel>();
    }
}
