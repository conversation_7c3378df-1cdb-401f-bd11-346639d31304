﻿namespace SimpleBooks.API.Controllers.Business.Warehouse
{
    public class ProductUnitController : BaseBusinessController<ProductUnitModel, ProductUnitModel, CreateProductUnitViewModel, UpdateProductUnitViewModel>
    {
        private readonly IProductUnitService _productUnitService;

        public ProductUnitController(IProductUnitService productUnitService) : base(productUnitService)
        {
            _productUnitService = productUnitService;
        }
    }
}
