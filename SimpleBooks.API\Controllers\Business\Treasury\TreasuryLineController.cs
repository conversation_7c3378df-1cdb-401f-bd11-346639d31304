﻿namespace SimpleBooks.API.Controllers.Business.Treasury
{
    public class TreasuryLineController : BaseBusinessController<TreasuryLineModel, TreasuryLineModel, CreateTreasuryLineViewModel, UpdateTreasuryLineViewModel>
    {
        private readonly ITreasuryLineService _treasuryLineService;

        public TreasuryLineController(ITreasuryLineService treasuryLineService) : base(treasuryLineService)
        {
            _treasuryLineService = treasuryLineService;
        }

        [HttpGet(nameof(SelectiveTreasuryLineDtoListAsync))]
        public async Task<IActionResult> SelectiveTreasuryLineDtoListAsync()
        {
            try
            {
                var result = await _treasuryLineService.SelectiveTreasuryLineDtoListAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet(nameof(GetByIdJsonAsync))]
        public async Task<IActionResult> GetByIdJsonAsync(string id)
        {
            try
            {
                Ulid ulid = Ulid.Parse(id);
                var result = await _treasuryLineService.GetByIdJsonAsync(ulid);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
