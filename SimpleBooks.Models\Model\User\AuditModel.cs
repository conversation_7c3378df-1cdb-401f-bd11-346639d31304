﻿namespace SimpleBooks.Models.Model.User
{
    [Table("Audit")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<AuditModel>))]
    public class AuditModel : BaseWithoutTrackingModel
    {
        [CustomRequired]
        [DisplayName("Type")]
        [MaxLength(10)]
        [Column(TypeName = "nvarchar(max)")]
        [Searchable(SearchableAttribute.SelectControls.ComboBox, nameof(Enums.AuditType))]
        public string Type { get; set; }
        [CustomRequired]
        [DisplayName("Table Name")]
        [MaxLength(50)]
        [Column(TypeName = "nvarchar(max)")]
        public string TableName { get; set; }
        [CustomRequired]
        [DisplayName("Date And Time")]
        [Column(TypeName = "datetime"), DataType(DataType.DateTime)]
        [Searchable(SearchableAttribute.SelectControls.DateTimePicker, "")]
        public DateTime DateTime { get; set; }
        [CustomRequired]
        [DisplayName("Old Values")]
        [Column(TypeName = "LongText")]
        public string OldValues { get; set; }
        [CustomRequired]
        [DisplayName("New Values")]
        [Column(TypeName = "LongText")]
        public string NewValues { get; set; }
        [CustomRequired]
        [DisplayName("Affected Columns")]
        [Column(TypeName = "LongText")]
        public string AffectedColumns { get; set; }
        [DisplayName("Primary Key")]
        [Column(TypeName = "LongText")]
        public string PrimaryKey { get; set; }
    }
}
