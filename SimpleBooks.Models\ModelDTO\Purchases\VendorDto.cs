﻿namespace SimpleBooks.Models.ModelDTO.Purchases
{
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<VendorDto>))]
    public class VendorDto
    {
        public Ulid Id { get; set; }
        public string VendorName { get; set; }
        public string VendorTaxCardNumber { get; set; }
        public Ulid? VendorTypeId { get; set; }
        public Ulid? PaymentTermId { get; set; }
    }
}
