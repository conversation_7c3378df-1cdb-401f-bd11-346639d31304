﻿namespace SimpleBooks.Models.Model.Warehouse
{
    [Table("ProductType")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<ProductTypeModel>))]
    public class ProductTypeModel : BaseWithoutTrackingModel
    {
        [CustomRequired]
        [DisplayName("Product Type Name")]
        public string ProductTypeName { get; set; }

        public virtual ICollection<ProductModel> Products { get; set; } = new List<ProductModel>();
    }
}
