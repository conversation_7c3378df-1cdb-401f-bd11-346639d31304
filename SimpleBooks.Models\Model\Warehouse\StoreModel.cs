﻿namespace SimpleBooks.Models.Model.Warehouse
{
    [Table("Store")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<StoreModel>))]
    public class StoreModel : BaseWithoutTrackingModel
    {
        [CustomRequired]
        [DisplayName("Store Name")]
        public string StoreName { get; set; }

        public virtual ICollection<InventoryModel> Inventories { get; set; } = new List<InventoryModel>();
    }
}
