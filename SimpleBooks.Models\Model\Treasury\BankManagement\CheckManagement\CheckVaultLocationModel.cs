﻿namespace SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement
{
    [Table("CheckVaultLocation")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<CheckVaultLocationModel>))]
    public class CheckVaultLocationModel : BaseWithoutTrackingModel
    {
        [CustomRequired]
        [DisplayName("Check Vault Account Number")]
        public string CheckVaultLocationNumber { get; set; }
        [CustomRequired]
        [DisplayName("Check Vault Account Currency")]
        public string CheckVaultLocationCurrency { get; set; }

        [CustomRequired]
        [DisplayName("Check Vault")]
        public Ulid CheckVaultId { get; set; }
        public virtual CheckVaultModel? CheckVault { get; set; }

        [DisplayName("Check Treasury Vouchers")]
        public virtual ICollection<CheckTreasuryVoucherModel> CheckTreasuryVouchers { get; set; } = new List<CheckTreasuryVoucherModel>();
        [DisplayName("Check Returns")]
        public virtual ICollection<CheckReturnModel> CheckReturns { get; set; } = new List<CheckReturnModel>();
    }
}
