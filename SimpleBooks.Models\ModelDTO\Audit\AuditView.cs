﻿namespace SimpleBooks.Models.ModelDTO.Audit
{
    public class AuditView<T>
    {
        public Ulid EmployeeId { get; private set; }
        public Dictionary<string, object> KeyValues { get; private set; } = new Dictionary<string, object>();
        public T? OldValues { get; private set; }
        public T? NewValues { get; private set; }
        public Enums.AuditType AuditType { get; private set; }
        public List<string> ChangedColumns { get; private set; } = new List<string>();

        public static AuditView<T> FromAudit(AuditModel auditModel)
        {
            var auditView = new AuditView<T>()
            {
                AuditType = Enums.GetEnumValue<Enums.AuditType>(auditModel.Type),
                KeyValues = JsonConvert.DeserializeObject<Dictionary<string, object>>(auditModel.PrimaryKey) ?? new Dictionary<string, object>(),
                OldValues = JsonConvert.DeserializeObject<T>(auditModel.OldValues),
                NewValues = JsonConvert.DeserializeObject<T>(auditModel.NewValues),
                ChangedColumns = JsonConvert.DeserializeObject<List<string>>(auditModel.AffectedColumns) ?? new List<string>(),
            };
            return auditView;
        }

        public static ICollection<AuditView<T>> FromAudits(ICollection<AuditModel> auditModels)
        {
            return auditModels.Select(FromAudit).ToList();
        }
    }
}
