﻿namespace SimpleBooks.Models.Model.Treasury
{
    [Table("Expenses")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<ExpensesModel>))]
    public partial class ExpensesModel : BaseModel
    {
        [CustomRequired]
        [DisplayName("Expenses Name")]
        public string ExpensesName { get; set; }
        [DisplayName("Expenses Description")]
        public string? ExpensesDescription { get; set; }

        public virtual ICollection<TreasuryLineModel> TreasuryLines { get; set; } = new List<TreasuryLineModel>();
    }
}
