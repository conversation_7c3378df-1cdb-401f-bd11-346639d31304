﻿namespace SimpleBooks.API.Controllers.Business.Purchases
{
    public class BillReturnController : BaseBusinessController<BillReturnModel, IndexBillReturnViewModel, CreateBillReturnViewModel, UpdateBillReturnViewModel>
    {
        private readonly IBillReturnService _billReturnService;

        public BillReturnController(IBillReturnService billReturnService) : base(billReturnService)
        {
            _billReturnService = billReturnService;
        }

        [HttpGet(nameof(GetReportBillReturnViewModelAsync))]
        public async Task<IActionResult> GetReportBillReturnViewModelAsync(string id)
        {
            try
            {
                Ulid ulid = Ulid.Parse(id);
                var result = await _billReturnService.GetReportBillReturnViewModelAsync(ulid);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }
    }
}
