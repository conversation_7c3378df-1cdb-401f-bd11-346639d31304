﻿namespace SimpleBooks.Models.Model.Purchases
{
    [Table("PurchaseOrderLine")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<PurchaseOrderLineModel>))]
    public class PurchaseOrderLineModel : BaseWithoutTrackingModel
    {
        [CustomRequired]
        [DisplayName("Quantity")]
        public decimal Quantity { get; set; }
        [CustomRequired]
        [DisplayName("Price")]
        public decimal Price { get; set; }
        [DisplayName("Is Percentage Discount")]
        public bool IsPercentageDiscount { get; set; }
        [DisplayName("Discount Rate")]
        public decimal DiscountRate { get; set; }
        [DisplayName("Discount Amount")]
        public decimal DiscountAmount { get; set; }
        [CustomRequired]
        [DisplayName("Amount")]
        public decimal Amount { get; set; }
        [CustomRequired]
        [DisplayName("Unit Qty Ratio")]
        public decimal UnitQtyRatio { get; set; }

        [CustomRequired]
        [DisplayName("Product")]
        public Ulid ProductId { get; set; }
        public virtual ProductModel? Product { get; set; }

        [CustomRequired]
        [DisplayName("Product Unit")]
        public Ulid ProductUnitId { get; set; }
        public virtual UnitModel? ProductUnit { get; set; }

        [DisplayName("PurchaseOrder")]
        public Ulid PurchaseOrderId { get; set; }
        public virtual PurchaseOrderModel? PurchaseOrder { get; set; }

        [DisplayName("Inventories")]
        public virtual ICollection<InventoryModel> Inventories { get; set; } = new List<InventoryModel>();
    }
}
