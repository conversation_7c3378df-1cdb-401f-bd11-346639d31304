﻿@{
    Ulid? id = Model;
    if (id != null && id != Ulid.Empty)
    {
        <td class="overflow-hidden align-middle">
            <div class="d-flex justify-content-end">
                <a class="btn btn-primary rounded rounded-3 me-2" asp-action="GetEntityReport" asp-route-id="@id" title="Print" target="_blank">
                    <i class="bi bi-printer"></i>
                </a>
                <a class="btn btn-info rounded rounded-3 me-2" asp-action="Update" asp-route-id="@id" title="Update">
                    <i class="bi bi-pencil-fill"></i>
                </a>
                <a href="javascript:;" class="btn btn-danger rounded rounded-3 js-delete" data-id="@id" title="Delete">
                    <i class="bi bi-trash3"></i>
                </a>
            </div>
        </td>
    }
}
