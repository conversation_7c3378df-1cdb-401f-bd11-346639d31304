﻿namespace SimpleBooks.API.Controllers.Business.Treasury.BankManagement.BankTransferManagement
{
    public class BankTransferTreasuryVoucherController : BaseBusinessController<BankTransferTreasuryVoucherModel, BankTransferTreasuryVoucherModel, CreateBankTransferTreasuryVoucherViewModel, UpdateBankTransferTreasuryVoucherViewModel>
    {
        private readonly IBankTransferTreasuryVoucherService _bankTransferTreasuryVoucherService;

        public BankTransferTreasuryVoucherController(IBankTransferTreasuryVoucherService bankTransferTreasuryVoucherService) : base(bankTransferTreasuryVoucherService)
        {
            _bankTransferTreasuryVoucherService = bankTransferTreasuryVoucherService;
        }

        [HttpGet(nameof(SelectiveBankTransferTreasuryVoucherDtoListAsync))]
        public async Task<IActionResult> SelectiveBankTransferTreasuryVoucherDtoListAsync()
        {
            try
            {
                var result = await _bankTransferTreasuryVoucherService.SelectiveBankTransferTreasuryVoucherDtoListAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet(nameof(GetByIdJsonAsync))]
        public async Task<IActionResult> GetByIdJsonAsync(string id)
        {
            try
            {
                Ulid ulid = Ulid.Parse(id);
                var result = await _bankTransferTreasuryVoucherService.GetByIdJsonAsync(ulid);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
