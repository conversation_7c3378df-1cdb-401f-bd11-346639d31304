﻿namespace SimpleBooks.Models.ModelMapper.Purchases
{
    public static class BillReturnMapper
    {
        public static CreateBillReturnViewModel ToCreateDto(this BillReturnModel entity)
        {
            CreateBillReturnViewModel viewModel = new CreateBillReturnViewModel()
            {
                BillReturnId = entity.BillReturnId,
                BillReturnDate = entity.BillReturnDate,
                BillReturnDueDate = entity.BillReturnDueDate,
                VendorId = entity.VendorId,
                VendorTypeId = entity.VendorTypeId,
                PaymentTermId = entity.PaymentTermId,
                ShipViaId = entity.ShipViaId,
                DeliveredById = entity.DeliveredById,
                Inventories = entity.Inventories.Select(i => i.ToCreateDto()).ToList(),
            };
            return viewModel;
        }

        public static BillReturnModel ToEntity(this CreateBillReturnViewModel entity)
        {
            BillReturnModel model = new BillReturnModel()
            {
                BillReturnId = entity.BillReturnId,
                BillReturnDate = entity.BillReturnDate,
                BillReturnDueDate = entity.BillReturnDueDate,
                VendorId = entity.VendorId,
                VendorTypeId = entity.VendorTypeId,
                PaymentTermId = entity.PaymentTermId,
                ShipViaId = entity.ShipViaId,
                DeliveredById = entity.DeliveredById,
                Inventories = entity.Inventories.Select(i => i.ToEntity()).ToList(),
            };
            return model;
        }

        public static UpdateBillReturnViewModel ToUpdateDto(this BillReturnModel entity)
        {
            UpdateBillReturnViewModel viewModel = new UpdateBillReturnViewModel()
            {
                Id = entity.Id,
                BillReturnId = entity.BillReturnId,
                BillReturnDate = entity.BillReturnDate,
                BillReturnDueDate = entity.BillReturnDueDate,
                VendorId = entity.VendorId,
                VendorTypeId = entity.VendorTypeId,
                PaymentTermId = entity.PaymentTermId,
                ShipViaId = entity.ShipViaId,
                DeliveredById = entity.DeliveredById,
                Inventories = entity.Inventories.Select(i => i.ToUpdateDto()).ToList(),
            };
            return viewModel;
        }

        public static BillReturnModel ToEntity(this UpdateBillReturnViewModel entity)
        {
            BillReturnModel model = new BillReturnModel()
            {
                Id = entity.Id,
                BillReturnId = entity.BillReturnId,
                BillReturnDate = entity.BillReturnDate,
                BillReturnDueDate = entity.BillReturnDueDate,
                VendorId = entity.VendorId,
                VendorTypeId = entity.VendorTypeId,
                PaymentTermId = entity.PaymentTermId,
                ShipViaId = entity.ShipViaId,
                DeliveredById = entity.DeliveredById,
                Inventories = entity.Inventories.Select(i => i.ToEntity()).ToList(),
            };
            return model;
        }
    }
}
