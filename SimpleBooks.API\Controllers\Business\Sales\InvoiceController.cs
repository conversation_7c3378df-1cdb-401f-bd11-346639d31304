﻿namespace SimpleBooks.API.Controllers.Business.Sales
{
    public class InvoiceController : BaseBusinessController<InvoiceModel, IndexInvoiceViewModel, CreateInvoiceViewModel, UpdateInvoiceViewModel>
    {
        private readonly IInvoiceService _invoiceService;

        public InvoiceController(IInvoiceService invoiceService) : base(invoiceService)
        {
            _invoiceService = invoiceService;
        }

        [HttpGet(nameof(GetOpenSalesOrdersByCustomerAsync))]
        public async Task<IActionResult> GetOpenSalesOrdersByCustomerAsync(string customerId)
        {
            try
            {
                Ulid ulid = Ulid.Parse(customerId);
                var result = await _invoiceService.GetOpenSalesOrdersByCustomerAsync(ulid);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpGet(nameof(GetOpenSalesOrderLinesAsync))]
        public async Task<IActionResult> GetOpenSalesOrderLinesAsync(string salesOrderId)
        {
            try
            {
                Ulid ulid = Ulid.Parse(salesOrderId);
                var result = await _invoiceService.GetOpenSalesOrderLinesAsync(ulid);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpGet(nameof(GetReportInvoiceViewModelAsync))]
        public async Task<IActionResult> GetReportInvoiceViewModelAsync(string id)
        {
            try
            {
                Ulid ulid = Ulid.Parse(id);
                var result = await _invoiceService.GetReportInvoiceViewModelAsync(ulid);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }
    }
}
