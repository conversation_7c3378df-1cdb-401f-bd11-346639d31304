﻿namespace SimpleBooks.Models.Enumerations
{
    public class BeneficiaryTypeEnumeration : UlidEnumeration<BeneficiaryTypeEnumeration>
    {
        public static readonly BeneficiaryTypeEnumeration Vendor = new BeneficiaryTypeEnumeration(Ulid.Parse("01JTKF9QSYN1TR6DMBY0MZ3BY6"), "Vendor");
        public static readonly BeneficiaryTypeEnumeration Customer = new BeneficiaryTypeEnumeration(Ulid.Parse("01JTKF9ZC7W32GQAFRSVP00D47"), "Customer");
        public static readonly BeneficiaryTypeEnumeration Employee = new BeneficiaryTypeEnumeration(Ulid.Parse("01JTKFA29V6V59R8C3DYMX90M3"), "Employee");

        private BeneficiaryTypeEnumeration(Ulid key, string value) : base(key, value)
        {
        }

        public static List<BeneficiaryTypeEnumeration> BeneficiaryTypeEnumerations
        {
            get => new List<BeneficiaryTypeEnumeration>
            {
                Vendor, Customer, Employee,
            };
        }
    }
}
