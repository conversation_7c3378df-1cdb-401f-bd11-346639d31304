﻿namespace SimpleBooks.Models.Model.Warehouse
{
    [Table("ProductUnit")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<ProductUnitModel>))]
    public class ProductUnitModel : BaseWithoutTrackingModel
    {
        [CustomRequired]
        [DisplayName("Product Unit Sales Price")]
        public decimal ProductUnitSalesPrice { get; set; }
        [CustomRequired]
        [DisplayName("Product Unit Ratio")]
        public decimal ProductUnitRatio { get; set; }

        [CustomRequired]
        [DisplayName("Product")]
        public Ulid ProductId { get; set; }
        public virtual ProductModel? Product { get; set; }

        [CustomRequired]
        [DisplayName("Product Unit")]
        public Ulid ProductUnitId { get; set; }
        public virtual UnitModel? ProductUnit { get; set; }
    }
}
