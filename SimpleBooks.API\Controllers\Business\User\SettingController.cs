﻿namespace SimpleBooks.API.Controllers.Business.User
{
    public class SettingController : BaseBusinessController<SettingModel, SettingModel, CreateSettingViewModel, UpdateSettingViewModel>
    {
        private readonly ISettingService _settingService;

        public SettingController(ISettingService settingService) : base(settingService)
        {
            _settingService = settingService;
        }
    }
}
