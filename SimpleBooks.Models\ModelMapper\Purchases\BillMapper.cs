﻿namespace SimpleBooks.Models.ModelMapper.Purchases
{
    public static class BillMapper
    {
        public static CreateBillViewModel ToCreateDto(this BillModel entity)
        {
            CreateBillViewModel viewModel = new CreateBillViewModel()
            {
                BillId = entity.BillId,
                BillDate = entity.BillDate,
                BillDueDate = entity.BillDueDate,
                VendorId = entity.VendorId,
                VendorTypeId = entity.VendorTypeId,
                PaymentTermId = entity.PaymentTermId,
                ShipViaId = entity.ShipViaId,
                DeliveredById = entity.DeliveredById,
                Inventories = entity.Inventories.Select(i => i.ToCreateDto()).ToList(),
            };
            return viewModel;
        }

        public static BillModel ToEntity(this CreateBillViewModel entity)
        {
            BillModel model = new BillModel()
            {
                BillId = entity.BillId,
                BillDate = entity.BillDate,
                BillDueDate = entity.BillDueDate,
                VendorId = entity.VendorId,
                VendorTypeId = entity.VendorTypeId,
                PaymentTermId = entity.PaymentTermId,
                ShipViaId = entity.ShipViaId,
                DeliveredById = entity.DeliveredById,
                Inventories = entity.Inventories.Select(i => i.ToEntity()).ToList(),
            };
            return model;
        }

        public static UpdateBillViewModel ToUpdateDto(this BillModel entity)
        {
            UpdateBillViewModel viewModel = new UpdateBillViewModel()
            {
                Id = entity.Id,
                BillId = entity.BillId,
                BillDate = entity.BillDate,
                BillDueDate = entity.BillDueDate,
                VendorId = entity.VendorId,
                VendorTypeId = entity.VendorTypeId,
                PaymentTermId = entity.PaymentTermId,
                ShipViaId = entity.ShipViaId,
                DeliveredById = entity.DeliveredById,
                Inventories = entity.Inventories.Select(i => i.ToUpdateDto()).ToList(),
            };
            return viewModel;
        }

        public static BillModel ToEntity(this UpdateBillViewModel entity)
        {
            BillModel model = new BillModel()
            {
                Id = entity.Id,
                BillId = entity.BillId,
                BillDate = entity.BillDate,
                BillDueDate = entity.BillDueDate,
                VendorId = entity.VendorId,
                VendorTypeId = entity.VendorTypeId,
                PaymentTermId = entity.PaymentTermId,
                ShipViaId = entity.ShipViaId,
                DeliveredById = entity.DeliveredById,
                Inventories = entity.Inventories.Select(i => i.ToEntity()).ToList(),
            };
            return model;
        }
    }
}
