﻿namespace SimpleBooks.API.Controllers.Business.Warehouse
{
    public class ProductTaxController : BaseBusinessController<ProductTaxModel, ProductTaxModel, CreateProductTaxViewModel, UpdateProductTaxViewModel>
    {
        private readonly IProductTaxService _productTaxService;

        public ProductTaxController(IProductTaxService productTaxService) : base(productTaxService)
        {
            _productTaxService = productTaxService;
        }
    }
}
